import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
    plugins: [react()],
    resolve: {
        alias: {
            '@': resolve(__dirname, './src'),
            components: resolve(__dirname, './src/components'),
            pages: resolve(__dirname, './src/pages'),
            utils: resolve(__dirname, './src/utils'),
            services: resolve(__dirname, './src/services'),
            hooks: resolve(__dirname, './src/hooks'),
            constants: resolve(__dirname, './src/constants'),
            types: resolve(__dirname, './src/types'),
            store: resolve(__dirname, './src/store'),
            assets: resolve(__dirname, './src/assets'),
            contexts: resolve(__dirname, './src/contexts'),
            layout: resolve(__dirname, './src/layout'),
            themes: resolve(__dirname, './src/themes'),
            containers: resolve(__dirname, './src/containers'),
            'menu-items': resolve(__dirname, './src/menu-items'),
            app: resolve(__dirname, './src/app'),
            index: resolve(__dirname, './src/index.tsx'),
            App: resolve(__dirname, './src/App.tsx'),
            Routes: resolve(__dirname, './src/Routes.tsx')
        }
    },
    server: {
        port: 3000,
        open: true
    },
    build: {
        outDir: 'dist',
        assetsDir: 'assets',
        sourcemap: false,
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['react', 'react-dom'],
                    mui: ['@mui/material', '@mui/icons-material', '@mui/lab'],
                    redux: ['@reduxjs/toolkit', 'react-redux', 'redux-persist', 'redux-saga']
                }
            }
        }
    },
    css: {
        preprocessorOptions: {
            scss: {
                silenceDeprecations: ['legacy-js-api', 'import'] // Silence deprecation warnings
            }
        }
    }
});
