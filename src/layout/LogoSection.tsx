import { Link as RouterLink } from 'react-router-dom';

// material-ui
import { Link, Box, Typography, useMediaQuery, useTheme } from '@mui/material';

// project imports
import { DASHBOARD_PATH } from 'constants/Config';
import Logo from '@/components/Logo';

// ==============================|| WORKFINDER LOGO SECTION ||============================== //

const LogoSection = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    return (
        <Link
            component={RouterLink}
            to={DASHBOARD_PATH}
            sx={{
                textDecoration: 'none',
                display: 'flex',
                alignItems: 'center',
                '&:hover': {
                    textDecoration: 'none'
                }
            }}
        >
            <Logo width={isMobile ? 140 : 183} height={isMobile ? 30 : 40} />
        </Link>
    );
};

export default LogoSection;
