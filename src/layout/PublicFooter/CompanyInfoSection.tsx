// material-ui
import { Box, Typography, useTheme } from '@mui/material';

// project imports
import Logo from '@/components/Logo';

// ==============================|| COMPANY INFO SECTION ||============================== //

const CompanyInfoSection = () => {
    const theme = useTheme();

    return (
        <Box sx={{ mb: 3 }}>
            {/* Logo */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Logo width={183} height={40} />
            </Box>

            <Typography
                sx={{
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: 2.14,
                    color: theme.palette.text.secondary,
                    mb: 2
                }}
            >
                329 Queensberry Street, North Melbourne VIC 3051, Australia. <EMAIL>
            </Typography>

            <Typography
                sx={{
                    fontWeight: 500,
                    fontSize: 18,
                    lineHeight: 1.56,
                    color: theme.palette.text.primary
                }}
            >
                Call us
                <br />
                123 456 7890
            </Typography>
        </Box>
    );
};

export default CompanyInfoSection;
