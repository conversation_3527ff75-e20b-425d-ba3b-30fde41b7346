// material-ui
import { Box, Typography, IconButton, useTheme } from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import InstagramIcon from '@mui/icons-material/Instagram';

// ==============================|| BOTTOM SECTION ||============================== //

const BottomSection = () => {
    const theme = useTheme();

    const socialLinks = [
        { icon: <FacebookIcon fontSize="small" />, href: 'https://facebook.com', label: 'Facebook' },
        { icon: <TwitterIcon fontSize="small" />, href: 'https://twitter.com', label: 'Twitter', isActive: true },
        { icon: <LinkedInIcon fontSize="small" />, href: 'https://linkedin.com', label: 'LinkedIn' },
        { icon: <InstagramIcon fontSize="small" />, href: 'https://instagram.com', label: 'Instagram' }
    ];

    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                flexWrap: 'wrap',
                gap: 2
            }}
        >
            <Typography
                sx={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: theme.palette.text.secondary
                }}
            >
                © 2021 Superio. All Right Reserved.
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {socialLinks.map((social, index) => (
                    <IconButton
                        key={index}
                        component="a"
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        aria-label={social.label}
                        sx={{
                            color: social.isActive ? '#BC91E8' : theme.palette.text.secondary,
                            '&:hover': {
                                color: social.isActive ? theme.palette.primary.main : '#BC91E8'
                            }
                        }}
                    >
                        {social.icon}
                    </IconButton>
                ))}
            </Box>
        </Box>
    );
};

export default BottomSection;
