import { Outlet } from 'react-router-dom';

// material-ui
import { Box, CssBaseline, useMediaQuery } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import PublicHeader from './PublicHeader';
import PublicFooter from './PublicFooter';

// ==============================|| MAIN LAYOUT (PUBLIC) ||============================== //

const MainLayout = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            <CssBaseline />

            {/* Public Header */}
            <PublicHeader />

            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    backgroundColor: theme.palette.background.default,
                    paddingTop: isMobile ? '80px' : '0px'
                }}
            >
                <Outlet />
            </Box>

            {/* Public Footer */}
            <PublicFooter />
        </Box>
    );
};

export default MainLayout;
