import { useNavigate, useLocation } from 'react-router-dom';

// material-ui
import { IconButton, Box, List, ListItem, ListItemText, Button, Link, useTheme, Collapse, Divider, Stack, Container } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';

// ==============================|| MOBILE MENU CONTENT ||============================== //

interface MobileMenuProps {
    open: boolean;
    onNavigate: (path: string) => void;
}

const MobileMenu = ({ open, onNavigate }: MobileMenuProps) => {
    const theme = useTheme();
    const location = useLocation();

    const navigationItems = [
        { title: 'Home', path: '/' },
        { title: 'Find Jobs', path: '/jobs' },
        { title: 'Employers', path: '/employers' },
        { title: 'Candidates', path: '/candidates' },
        { title: 'Blog', path: '/blog' },
        { title: 'Pages', path: '/pages' }
    ];

    return (
        <Collapse in={open}>
            <Box
                sx={{
                    backgroundColor: theme.palette.background.paper,
                    boxShadow: '0px 8px 16px rgba(0, 0, 0, 0.1)',
                    borderTop: `1px solid ${theme.palette.divider}`,
                    width: '100%'
                }}
            >
                <Container maxWidth="xl">
                    <Box sx={{ py: 3 }}>
                        {/* Navigation Links */}
                        <List sx={{ py: 0 }}>
                            {navigationItems.map((item) => {
                                const isActive = location.pathname === item.path;

                                return (
                                    <ListItem key={item.title} sx={{ px: 0, py: 1 }}>
                                        <ListItemText
                                            primary={item.title}
                                            onClick={() => onNavigate(item.path)}
                                            sx={{
                                                cursor: 'pointer',
                                                '& .MuiListItemText-primary': {
                                                    fontWeight: 400,
                                                    fontSize: 16,
                                                    color: isActive ? theme.palette.primary.main : theme.palette.text.primary,
                                                    '&:hover': {
                                                        color: theme.palette.primary.main
                                                    }
                                                }
                                            }}
                                        />
                                    </ListItem>
                                );
                            })}
                        </List>

                        <Divider sx={{ my: 2 }} />

                        {/* Action Buttons */}
                        <Stack spacing={2}>
                            <Link
                                href="#"
                                sx={{
                                    fontWeight: 400,
                                    fontSize: 15,
                                    color: theme.palette.primary.main,
                                    textDecoration: 'none',
                                    '&:hover': {
                                        textDecoration: 'underline'
                                    }
                                }}
                            >
                                Upload your CV
                            </Link>

                            <Button
                                onClick={() => onNavigate('/login')}
                                variant="outlined"
                                fullWidth
                                sx={{
                                    fontWeight: 400,
                                    fontSize: 15,
                                    color: theme.palette.primary.main,
                                    backgroundColor: 'rgba(25, 103, 210, 0.07)',
                                    border: 'none',
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    py: 1.5,
                                    '&:hover': {
                                        backgroundColor: 'rgba(25, 103, 210, 0.12)',
                                        border: 'none'
                                    }
                                }}
                            >
                                Login / Register
                            </Button>

                            <Button
                                onClick={() => onNavigate('/post-job')}
                                variant="contained"
                                fullWidth
                                sx={{
                                    fontWeight: 400,
                                    fontSize: 15,
                                    color: theme.palette.primary.contrastText,
                                    backgroundColor: theme.palette.primary.main,
                                    borderRadius: 2,
                                    textTransform: 'none',
                                    py: 1.5,
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.dark
                                    }
                                }}
                            >
                                Job Post
                            </Button>
                        </Stack>
                    </Box>
                </Container>
            </Box>
        </Collapse>
    );
};

// ==============================|| MOBILE SECTION ||============================== //

interface MobileSectionProps {
    open: boolean;
    onToggle: () => void;
    contentOnly?: boolean;
}

const MobileSection = ({ open, onToggle, contentOnly = false }: MobileSectionProps) => {
    const theme = useTheme();
    const navigate = useNavigate();

    const handleNavigate = (path: string) => {
        navigate(path);
        onToggle(); // Close menu after navigation
    };

    // If contentOnly is true, only render the menu content
    if (contentOnly) {
        return <MobileMenu open={open} onNavigate={handleNavigate} />;
    }

    // Otherwise, only render the button
    return (
        <IconButton
            aria-label="toggle mobile menu"
            onClick={onToggle}
            sx={{
                color: theme.palette.text.primary,
                p: 1
            }}
        >
            {open ? <CloseIcon /> : <MenuIcon />}
        </IconButton>
    );
};

export default MobileSection;
