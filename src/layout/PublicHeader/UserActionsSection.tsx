import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// material-ui
import { Button, Stack, IconButton, Menu, MenuItem, Typography, Divider, Link, useTheme } from '@mui/material';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

// project imports
import useAuth from 'hooks/useAuth';

// ==============================|| USER ACTIONS SECTION ||============================== //

const UserActionsSection = () => {
    const theme = useTheme();
    const navigate = useNavigate();
    const auth = useAuth();

    const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null);

    const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const handleLogin = () => {
        navigate('/login');
        handleCloseUserMenu();
    };

    const handleRegister = () => {
        navigate('/register');
        handleCloseUserMenu();
    };

    const handlePostJob = () => {
        navigate('/post-job');
    };

    return (
        <Stack direction="row" spacing={2} alignItems="center">
            {/* Upload CV Link */}
            <Link
                href="#"
                sx={{
                    fontWeight: 400,
                    fontSize: 15,
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    '&:hover': {
                        textDecoration: 'underline'
                    }
                }}
            >
                Upload your CV
            </Link>

            {/* Login/Register Button */}
            <Button
                onClick={handleOpenUserMenu}
                sx={{
                    fontWeight: 400,
                    fontSize: 15,
                    color: theme.palette.primary.main,
                    backgroundColor: 'rgba(25, 103, 210, 0.07)',
                    px: 3,
                    py: 1,
                    borderRadius: 2,
                    textTransform: 'none',
                    '&:hover': {
                        backgroundColor: 'rgba(25, 103, 210, 0.12)'
                    }
                }}
            >
                Login / Register
            </Button>

            {/* Post Job Button */}
            <Button
                onClick={handlePostJob}
                sx={{
                    fontWeight: 400,
                    fontSize: 15,
                    color: theme.palette.primary.contrastText,
                    backgroundColor: theme.palette.primary.main,
                    px: 3,
                    py: 1,
                    borderRadius: 2,
                    textTransform: 'none',
                    '&:hover': {
                        backgroundColor: theme.palette.primary.dark
                    }
                }}
            >
                Job Post
            </Button>

            {/* User Menu */}
            <Menu
                anchorEl={anchorElUser}
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right'
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right'
                }}
            >
                <MenuItem onClick={handleLogin}>
                    <Typography>Login</Typography>
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleRegister}>
                    <Typography>Register</Typography>
                </MenuItem>
            </Menu>
        </Stack>
    );
};

export default UserActionsSection;
