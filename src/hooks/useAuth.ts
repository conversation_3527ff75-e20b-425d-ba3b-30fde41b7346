import { useAppSelector } from 'app/hooks';
import { ESTATUS_LOGIN } from 'constants/Common';
import { authSelector } from 'store/slice/authSlice';
import { getCookieByKey } from 'utils/cookies';

const useAuth = () => {
    const accessToken = getCookieByKey('accessToken');

    const { status } = useAppSelector(authSelector);

    if (accessToken && status === ESTATUS_LOGIN.SUCCESS) {
        return true;
    }

    return false;
};

export default useAuth;
