import { useEffect } from 'react';
import { Location, Navigate, useLocation, Link } from 'react-router-dom';

// material-ui
import { Stack, Typography, Box, Button } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { AuthForgotPassword } from 'containers/authentication';
import { ESTATUS_LOGIN } from 'constants/Common';
import { DASHBOARD_PATH } from 'constants/Config';

// ================================|| AUTH - FORGOT PASSWORD ||================================ //

const ForgotPassword = () => {
    const location = useLocation();
    const { status } = useAppSelector(authSelector);
    const theme = useTheme();

    const { state }: Location = location;

    if (status === ESTATUS_LOGIN.SUCCESS) {
        return <Navigate to={state ? `${state.from.pathname}${state.from?.search}` : DASHBOARD_PATH} />;
    }

    return (
        <Box sx={{ width: '100%' }}>
            {/* Forgot Password Header */}
            <Stack spacing={1} sx={{ mb: 4 }}>
                <Typography
                    variant="h2"
                    sx={{
                        fontWeight: 700,
                        fontSize: { xs: 28, md: 36 },
                        color: theme.palette.text.primary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    Forgot Password
                </Typography>
                <Typography
                    variant="body1"
                    sx={{
                        fontSize: 16,
                        color: theme.palette.text.secondary,
                        textAlign: { xs: 'center', md: 'left' }
                    }}
                >
                    Don't worry, we'll send you reset instructions
                </Typography>
            </Stack>

            {/* Forgot Password Form */}
            <AuthForgotPassword />

            {/* Forgot Password Footer */}
            <Stack direction="row" spacing={1} justifyContent="center" sx={{ mt: 4 }}>
                <Button
                    variant="text"
                    component={Link}
                    to="/login"
                    sx={{
                        textTransform: 'none',
                        color: theme.palette.text.secondary,
                        fontSize: 14,
                        fontWeight: 500,
                        p: 1,
                        '&:hover': {
                            backgroundColor: 'transparent',
                            color: theme.palette.primary.main
                        }
                    }}
                >
                    ← Back to Login
                </Button>
            </Stack>
        </Box>
    );
};

export default ForgotPassword;
