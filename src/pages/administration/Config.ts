export interface IDepartmentFilterConfig {
    // Add department filter configuration
    name?: string;
    status?: string;
}

export interface IProjectEditConfig {
    // Add project edit configuration
    name?: string;
    description?: string;
}

export interface IProjectSearchConfig {
    // Add project search configuration
    keyword?: string;
    status?: string;
}

export interface IProjectTypeFilterConfig {
    // Add project type filter configuration
    name?: string;
    status?: string;
}

export interface ITitleFilterConfig {
    // Add title filter configuration
    name?: string;
    department?: string;
}
