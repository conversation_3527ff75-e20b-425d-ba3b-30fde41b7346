import { Box } from '@mui/material';

// project imports
import {
    HeroSearch,
    CategoriesSection,
    FeaturedJobsSection,
    HowItWorksSection,
    PopularCompaniesSection,
    BlogSection,
    CTASection
} from '../../containers/homepage';

// ================================|| HOMEPAGE ||================================ //

const Homepage = () => {
    // TODO: Get authentication state from context/store
    const isLoggedIn = false; // This should come from auth context

    return (
        <Box>
            {/* Hero Section with Search */}
            <HeroSearch />

            {/* Popular Job Categories */}
            <CategoriesSection />

            {/* Featured Jobs */}
            <FeaturedJobsSection />

            {/* How It Works */}
            <HowItWorksSection />

            {/* Popular Companies */}
            <PopularCompaniesSection />

            {/* Recent News */}
            <BlogSection />

            {/* CTA Section - Only show when not logged in */}
            <CTASection isLoggedIn={isLoggedIn} />
        </Box>
    );
};

export default Homepage;
