// ================================|| HOMEPAGE CONFIGURATION ||================================ //

export interface IJobCategory {
    id: number;
    title: string;
    openPositions: number;
    icon: string;
    color?: string;
}

export interface IFeaturedJob {
    id: number;
    title: string;
    company: string;
    location: string;
    salary: string;
    type: string;
    tags: string[];
    postedTime: string;
    logo?: string;
}

export interface ITestimonial {
    id: number;
    name: string;
    role: string;
    content: string;
    rating: number;
    avatar?: string;
}

export interface ISiteStats {
    id: number;
    value: string;
    label: string;
    icon: string;
}

export interface INews {
    id: number;
    title: string;
    content: string;
    image?: string;
    date: string;
    comments: number;
}

// Mock Data
export const jobCategories: IJobCategory[] = [
    {
        id: 1,
        title: 'Accounting / Finance',
        openPositions: 2,
        icon: 'MoneyIcon',
        color: '#1967D2'
    },
    {
        id: 2,
        title: 'Marketing',
        openPositions: 86,
        icon: 'MegaphoneIcon',
        color: '#F9AB00'
    },
    {
        id: 3,
        title: 'Design',
        openPositions: 43,
        icon: 'DesignIcon',
        color: '#34A853'
    },
    {
        id: 4,
        title: 'Development',
        openPositions: 12,
        icon: 'DevelopmentIcon',
        color: '#9AA0A6'
    },
    {
        id: 5,
        title: 'Human Resource',
        openPositions: 55,
        icon: 'HumanResourceIcon',
        color: '#FBBC04'
    },
    {
        id: 6,
        title: 'Project Management',
        openPositions: 19,
        icon: 'ProjectManagementIcon',
        color: '#EA4335'
    },
    {
        id: 7,
        title: 'Customer Service',
        openPositions: 72,
        icon: 'CustomerServiceIcon',
        color: '#1967D2'
    },
    {
        id: 8,
        title: 'Health and Care',
        openPositions: 25,
        icon: 'HealthCareIcon',
        color: '#34A853'
    },
    {
        id: 9,
        title: 'Automotive Jobs',
        openPositions: 92,
        icon: 'AutomotiveIcon',
        color: '#FBBC04'
    }
];

export const featuredJobs: IFeaturedJob[] = [
    {
        id: 1,
        title: 'Software Engineer (Android), Libraries',
        company: 'Segment',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Full Time',
        tags: ['Private', 'Urgent'],
        postedTime: '11 hours ago'
    },
    {
        id: 2,
        title: 'Recruiting Coordinator',
        company: 'Catalyst',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Full Time',
        tags: [],
        postedTime: '11 hours ago'
    },
    {
        id: 3,
        title: 'Product Manager, Studio',
        company: 'InVision',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Full Time',
        tags: [],
        postedTime: '11 hours ago'
    },
    {
        id: 4,
        title: 'Senior Product Designer',
        company: 'Upwork',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Part Time',
        tags: ['Private'],
        postedTime: '11 hours ago'
    },
    {
        id: 5,
        title: 'Senior Full Stack Engineer, Creator Success , Al Engineer , Engineer',
        company: 'Medium',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Full Time',
        tags: ['Urgent'],
        postedTime: '11 hours ago'
    },
    {
        id: 6,
        title: 'Web Developer',
        company: 'Figma',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Part Time',
        tags: [],
        postedTime: '11 hours ago'
    },
    {
        id: 4,
        title: 'Senior Product Designer',
        company: 'Upwork',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Part Time',
        tags: ['Private'],
        postedTime: '11 hours ago'
    },
    {
        id: 5,
        title: 'Senior Full Stack Engineer, Creator Success',
        company: 'Medium',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Full Time',
        tags: ['Urgent'],
        postedTime: '11 hours ago'
    },
    {
        id: 6,
        title: 'Web Developer',
        company: 'Figma',
        location: 'London, UK',
        salary: '$35k - $45k',
        type: 'Part Time',
        tags: [],
        postedTime: '11 hours ago'
    }
];

export const testimonials: ITestimonial[] = [
    {
        id: 1,
        name: 'Nicole Wells',
        role: 'Web Developer',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        rating: 5
    },
    {
        id: 2,
        name: 'Gabriel Nolan',
        role: 'Consultant',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        rating: 5
    },
    {
        id: 3,
        name: 'Ashley Jenkins',
        role: 'Designer',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        rating: 5
    }
];

export const siteStats: ISiteStats[] = [
    {
        id: 1,
        value: '4M',
        label: '4 million daily active users',
        icon: 'TrendingUp'
    },
    {
        id: 2,
        value: '12k',
        label: 'Over 12k open job positions',
        icon: 'Work'
    },
    {
        id: 3,
        value: '20M',
        label: 'Over 20 million stories shared',
        icon: 'Forum'
    }
];

export const recentNews: INews[] = [
    {
        id: 1,
        title: 'Attract Sales And Profits',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        date: 'August 31, 2021',
        comments: 12
    },
    {
        id: 2,
        title: '5 Tips For Your Job Interviews',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        date: 'August 31, 2021',
        comments: 12
    },
    {
        id: 3,
        title: 'An Overworked Newspaper Editor',
        content: 'A job ravenously while Far much that one rank beheld after outside....',
        date: 'August 31, 2021',
        comments: 12
    }
];

// Search Configuration
export interface IHomepageSearchConfig {
    keywords: string;
    location: string;
    category: string;
}

export const homepageSearchConfig: IHomepageSearchConfig = {
    keywords: '',
    location: '',
    category: ''
};

// Popular searches
export const popularSearches = ['Designer', 'Developer', 'Web', 'IOS', 'PHP', 'Senior', 'Engineer'];

// Validation schema
import * as yup from 'yup';

export const homepageSearchSchema = yup.object().shape({
    keywords: yup.string().trim(),
    location: yup.string().trim(),
    category: yup.string().trim()
});
