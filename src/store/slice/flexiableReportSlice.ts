import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { IMessageResponse, IOption, IResponseList } from 'types';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';
import {
    IColumnConfig,
    IGetColumnNameResponse,
    IGetReportNameResponse,
    IGetTextConfigResponse,
    ISearchColumnConfigParams,
    ISearchTextConfigParams,
    ITextConfig
} from 'types/flexible-report';

export const getListColumns = createAsyncThunk<IResponseList<IGetColumnNameResponse>, ISearchColumnConfigParams>(
    'Api.project.flexiableReport.getListColumns',
    async (params) => {
        const response = await sendRequest(Api.flexible_columns.get, params);

        return response;
    }
);

export const editColumn = createAsyncThunk<IResponseList<{ content: IMessageResponse }>, IColumnConfig>(
    'Api.project.flexiableReport.editColumn',
    async (params) => {
        const response = await sendRequest(Api.flexible_columns.edit, params);

        return response;
    }
);

export const getListTextConfig = createAsyncThunk<IResponseList<IGetTextConfigResponse>, ISearchTextConfigParams>(
    'Api.project.flexiableReport.getListTextConfig',
    async (params) => {
        const response = await sendRequest(Api.flexible_textConfig.get, params);

        return response;
    }
);

export const editTextConfig = createAsyncThunk<IResponseList<{ content: IMessageResponse }>, ITextConfig>(
    'Api.project.flexiableReport.editTextConfig',
    async (params) => {
        const response = await sendRequest(Api.flexible_textConfig.edit, params);

        return response;
    }
);

export const getReportName = createAsyncThunk<IResponseList<IGetReportNameResponse>>(
    'Api.project.flexiableReport.getReportName',
    async (params) => {
        const response = await sendRequest(Api.flexible_report.get);

        return response;
    }
);

interface IReportNameOption extends IOption {
    isConditionVisible?: boolean;
    isCalculateVisible?: boolean;
}
interface IProjectTypeState {
    columnNames?: IResponseList<IGetColumnNameResponse>['result'];
    textConfig?: IResponseList<IGetTextConfigResponse>['result'];
    loading: { [key: string]: boolean };
    reportName?: IReportNameOption[];
}

const initialState: IProjectTypeState = {
    loading: {}
};

const flexiableReport = createSlice({
    name: 'flexible-report',
    initialState: initialState,
    reducers: {
        resetColumnsData: (state) => {
            state.columnNames = undefined;
        },
        resetTextConfigData: (state) => {
            state.textConfig = undefined;
        }
    },
    extraReducers: (builder) => {
        builder.addCase(getListColumns.pending, (state) => {
            state.loading[getListColumns.typePrefix] = true;
        });
        builder.addCase(getListColumns.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.columnNames = action.payload.result;
            }

            state.loading[getListColumns.typePrefix] = false;
        });
        builder.addCase(getListColumns.rejected, (state) => {
            state.loading[getListColumns.typePrefix] = false;
        });

        builder.addCase(editColumn.pending, (state) => {
            state.loading[editColumn.typePrefix] = true;
        });
        builder.addCase(editColumn.fulfilled, (state) => {
            state.loading[editColumn.typePrefix] = false;
        });
        builder.addCase(editColumn.rejected, (state) => {
            state.loading[editColumn.typePrefix] = false;
        });

        builder.addCase(getListTextConfig.pending, (state) => {
            state.loading[getListTextConfig.typePrefix] = true;
        });
        builder.addCase(getListTextConfig.fulfilled, (state, action) => {
            if (action.payload.status && Array.isArray(action.payload.result.content)) {
                state.textConfig = action.payload.result;
            }

            state.loading[getListTextConfig.typePrefix] = false;
        });
        builder.addCase(getListTextConfig.rejected, (state) => {
            state.loading[getListTextConfig.typePrefix] = false;
        });

        builder.addCase(editTextConfig.pending, (state) => {
            state.loading[editTextConfig.typePrefix] = true;
        });
        builder.addCase(editTextConfig.fulfilled, (state) => {
            state.loading[editTextConfig.typePrefix] = false;
        });
        builder.addCase(editTextConfig.rejected, (state) => {
            state.loading[editTextConfig.typePrefix] = false;
        });
        builder.addCase(getReportName.pending, (state) => {
            state.loading[getReportName.typePrefix] = true;
        });
        builder.addCase(getReportName.fulfilled, (state, action) => {
            state.loading[getReportName.typePrefix] = false;
            state.reportName = action.payload.result.content.map((item) => ({
                label: item.reportName,
                value: item.id,
                isCalculateVisible: item.isCalculateVisible,
                isConditionVisible: item.isConditionVisible
            }));
        });
        builder.addCase(getReportName.rejected, (state) => {
            state.loading[getReportName.typePrefix] = false;
        });
    }
});

export default flexiableReport.reducer;

export const { resetColumnsData, resetTextConfigData } = flexiableReport.actions;

export const flexiableReportSelector = (state: RootState) => state.flexiableReport;
