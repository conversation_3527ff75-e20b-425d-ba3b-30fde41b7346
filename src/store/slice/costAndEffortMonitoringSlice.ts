import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { monthlyCostMonitoringInfoDefault } from 'pages/cost-monitoring/Config';
import { convertMonthFromToDate, convertWeekFromToDate } from 'utils/date';
import sendRequest from 'services/ApiService';
import { RootState } from 'app/store';
import Api from 'constants/Api';
import {
    IMonthlyCostMonitoringRequest,
    IMonthlyCostMonitoringResponse,
    IOption,
    IProjectList,
    IResponseList,
    IWeeklyCostMonitoringRequest,
    IWeeklyCostMonitoringResponse
} from 'types';

// interface
interface IProjectOptions extends IOption {
    billable?: string;
    dept?: string;
    projectType?: string;
}

interface ICostAndEffortMonitoringState {
    loading: { [key: string]: boolean };
    weeklyCostMonitoring?: IResponseList<IWeeklyCostMonitoringResponse>['result'];
    monthlyCostMonitoring: IResponseList<IMonthlyCostMonitoringResponse>['result']['content'];
    projectOptions: IProjectOptions[];
}

// initial state
const initialState: ICostAndEffortMonitoringState = {
    loading: {},
    projectOptions: [],
    monthlyCostMonitoring: monthlyCostMonitoringInfoDefault
};

export const getWeeklyCostMonitoring = createAsyncThunk<IResponseList<IWeeklyCostMonitoringResponse>, IWeeklyCostMonitoringRequest>(
    Api.cost_monitoring.getEffortByWeek.url,
    async (params) => {
        const response = await sendRequest(Api.cost_monitoring.getEffortByWeek, params);

        return response;
    }
);

export const getMonthlyCostMonitoring = createAsyncThunk<IResponseList<IMonthlyCostMonitoringResponse>, IMonthlyCostMonitoringRequest>(
    Api.cost_monitoring.getAll.url,
    async (params) => {
        const response = await sendRequest(Api.cost_monitoring.getAll, params);

        return response;
    }
);

export const getCostMonitoringProjectOption = createAsyncThunk<
    IResponseList<IProjectList>,
    {
        type: 'week' | 'month';
        value: string | number;
        color?: boolean;
    }
>('getCostMonitoringProjectOption', async (params) => {
    const param = params.type === 'week' ? convertWeekFromToDate(params.value) : convertMonthFromToDate(params.value.toString());
    const response = await sendRequest(Api.project.getAll, {
        ...param,
        projectAuthorization: true,
        size: 1000
    });

    return response;
});

export const getCostMonitoringProjectOptionByFixCost = createAsyncThunk<
    IResponseList<IProjectList>,
    {
        type: 'week' | 'month';
        value: string | number;
        fixCost: boolean;
        dept?: string;
        projectType?: string;
        color?: boolean;
    }
>('getCostMonitoringProjectOptionByFixCost', async (params) => {
    const param = params.type === 'week' ? convertWeekFromToDate(params.value) : convertMonthFromToDate(params.value.toString());
    const response = await sendRequest(Api.master.getProjectAll(params.fixCost), {
        ...param,
        dept: params.dept,
        projectType: params.projectType,
        projectAuthorization: true,
        size: 1000
    });

    return response;
});

const costAndEffortMonitoringSlice = createSlice({
    name: 'cost-and-effort-monitoring',
    initialState: initialState,
    reducers: {},
    extraReducers: (builder) => {
        // getWeeklyCostMonitoring
        builder.addCase(getWeeklyCostMonitoring.pending, (state) => {
            state.weeklyCostMonitoring = undefined;
            state.loading[getWeeklyCostMonitoring.typePrefix] = true;
        });
        builder.addCase(getWeeklyCostMonitoring.fulfilled, (state, action) => {
            state.loading[getWeeklyCostMonitoring.typePrefix] = false;
            if (action.payload.status) {
                state.weeklyCostMonitoring = action.payload.result;
            }
        });
        builder.addCase(getWeeklyCostMonitoring.rejected, (state) => {
            state.loading[getWeeklyCostMonitoring.typePrefix] = false;
        });

        // getMonthlyCostMonitoring
        builder.addCase(getMonthlyCostMonitoring.pending, (state) => {
            state.monthlyCostMonitoring = monthlyCostMonitoringInfoDefault;
            state.loading[getMonthlyCostMonitoring.typePrefix] = true;
        });
        builder.addCase(getMonthlyCostMonitoring.fulfilled, (state, action) => {
            state.loading[getMonthlyCostMonitoring.typePrefix] = false;
            if (action.payload.status) {
                state.monthlyCostMonitoring = action.payload.result.content;
            }
        });
        builder.addCase(getMonthlyCostMonitoring.rejected, (state) => {
            state.loading[getMonthlyCostMonitoring.typePrefix] = false;
        });

        // getCostMonitoringProjectOption
        builder.addCase(getCostMonitoringProjectOption.pending, (state) => {
            state.projectOptions = [];
            state.loading[getCostMonitoringProjectOption.typePrefix] = true;
        });
        builder.addCase(getCostMonitoringProjectOption.fulfilled, (state, action) => {
            state.loading[getCostMonitoringProjectOption.typePrefix] = false;
            if (action.payload?.status) {
                state.projectOptions = action.payload.result.content.map((prj) => ({
                    value: prj.projectId,
                    label: prj.projectName,
                    typeCode: prj.typeCode,
                    color: action.meta.arg.color ? prj.effortArise : undefined
                }));
            }
        });
        builder.addCase(getCostMonitoringProjectOption.rejected, (state) => {
            state.loading[getCostMonitoringProjectOption.typePrefix] = false;
        });

        // getCostMonitoringProjectOptionByFixCost
        builder.addCase(getCostMonitoringProjectOptionByFixCost.pending, (state) => {
            state.projectOptions = [];
            state.loading[getCostMonitoringProjectOptionByFixCost.typePrefix] = true;
        });
        builder.addCase(getCostMonitoringProjectOptionByFixCost.fulfilled, (state, action) => {
            state.loading[getCostMonitoringProjectOptionByFixCost.typePrefix] = false;
            if (action.payload?.status) {
                state.projectOptions = action.payload.result.content.map((prj) => ({
                    value: prj.projectId,
                    label: prj.projectName,
                    typeCode: prj.typeCode,
                    billable: prj.billable,
                    dept: prj.deptId,
                    projectType: prj.type,
                    color: action.meta.arg.color ? prj.effortArise : undefined
                }));
            }
        });
        builder.addCase(getCostMonitoringProjectOptionByFixCost.rejected, (state) => {
            state.loading[getCostMonitoringProjectOptionByFixCost.typePrefix] = false;
        });
    }
});

// export const {} = costAndEffortMonitoringSlice.actions;

// selectors
export const costAndEffortMonitoringSelector = (state: RootState) => state.costAndEffortMonitoring;

// reducers
export default costAndEffortMonitoringSlice.reducer;
