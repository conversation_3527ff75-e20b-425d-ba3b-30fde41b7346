import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// types
import { SnackbarProps } from 'types';

const initialState: SnackbarProps = {
    action: false,
    open: false,
    isMultipleLanguage: true,
    message: 'Note archived',
    anchorOrigin: {
        vertical: 'bottom',
        horizontal: 'right'
    },
    variant: 'default',
    alert: {
        color: 'info',
        variant: 'filled'
    },
    transition: 'Fade',
    close: true,
    actionButton: false,
    duration: 6000
};

// ==============================|| SLICE - SNACKBAR ||============================== //

const snackbarSlice = createSlice({
    name: 'snackbar',
    initialState,
    reducers: {
        openSnackbar(state, action: PayloadAction<Partial<SnackbarProps>>) {
            const { open, message, anchorOrigin, variant, alert, transition, close, actionButton, isMultipleLanguage, duration } =
                action.payload;
            state.action = !state.action;
            state.open = open || initialState.open;
            state.message = message ? message : initialState.message;
            state.isMultipleLanguage = isMultipleLanguage ? isMultipleLanguage : initialState.isMultipleLanguage;
            state.anchorOrigin = anchorOrigin || initialState.anchorOrigin;
            state.variant = variant || initialState.variant;
            state.alert = {
                color: alert?.color || initialState.alert.color,
                variant: alert?.variant || initialState.alert.variant
            };
            state.transition = transition || initialState.transition;
            state.close = close === false ? close : initialState.close;
            state.actionButton = actionButton || initialState.actionButton;
            state.duration = duration || initialState.duration;
        },

        closeSnackbar(state) {
            state.open = false;
        }
    }
});

export default snackbarSlice.reducer;

export const { closeSnackbar, openSnackbar } = snackbarSlice.actions;
