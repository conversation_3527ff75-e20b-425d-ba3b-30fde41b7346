// material-ui

// material-ui
import { Box, Container, Typography, Grid, Card, CardContent, useTheme } from '@mui/material';
import { Search, Person, Work } from '@mui/icons-material';

const steps = [
    {
        id: 1,
        icon: <Search sx={{ fontSize: 48 }} />,
        title: 'Tìm kiếm công việc',
        description: 'Tìm kiếm hàng nghìn công việc phù hợp với kỹ năng và kinh nghiệm của bạn'
    },
    {
        id: 2,
        icon: <Person sx={{ fontSize: 48 }} />,
        title: 'T<PERSON><PERSON> hồ sơ',
        description: '<PERSON><PERSON><PERSON> hồ sơ chuyên nghiệp để thu hút nhà tuyển dụng và tăng cơ hội được tuyển dụng'
    },
    {
        id: 3,
        icon: <Work sx={{ fontSize: 48 }} />,
        title: 'Ứng tuyển ngay',
        description: 'Ứng tuyển vào các vị trí yêu thích chỉ với một cú click và theo dõi tiến trình'
    }
];

const HowItWorksSection = () => {
    const theme = useTheme();

    return (
        <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: '#f8f9fa' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontWeight: 600,
                            color: theme.palette.primary.dark,
                            mb: 2,
                            fontSize: { xs: '2rem', md: '2.5rem' }
                        }}
                    >
                        Cách thức hoạt động
                    </Typography>
                    <Typography
                        variant="h6"
                        color="text.secondary"
                        sx={{
                            fontWeight: 400,
                            maxWidth: 600,
                            mx: 'auto'
                        }}
                    >
                        Chỉ với 3 bước đơn giản, bạn có thể tìm được công việc mơ ước
                    </Typography>
                </Box>

                {/* Steps Grid */}
                <Grid container spacing={4}>
                    {steps.map((step) => (
                        <Grid item xs={12} md={4} key={step.id}>
                            <Card
                                elevation={0}
                                sx={{
                                    height: '100%',
                                    textAlign: 'center',
                                    p: 4,
                                    backgroundColor: 'white',
                                    border: '1px solid',
                                    borderColor: 'divider',
                                    borderRadius: 2,
                                    transition: 'all 0.3s ease',
                                    position: 'relative',
                                    '&:hover': {
                                        transform: 'translateY(-8px)',
                                        boxShadow: '0px 20px 40px rgba(64, 79, 104, 0.15)',
                                        borderColor: theme.palette.primary.main
                                    }
                                }}
                            >
                                {/* Step Number */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: -20,
                                        left: '50%',
                                        transform: 'translateX(-50%)',
                                        width: 40,
                                        height: 40,
                                        borderRadius: '50%',
                                        backgroundColor: theme.palette.primary.main,
                                        color: 'white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontWeight: 600,
                                        fontSize: '1.2rem'
                                    }}
                                >
                                    {step.id}
                                </Box>

                                <CardContent sx={{ pt: 4 }}>
                                    {/* Icon */}
                                    <Box
                                        sx={{
                                            color: theme.palette.primary.main,
                                            mb: 3
                                        }}
                                    >
                                        {step.icon}
                                    </Box>

                                    {/* Title */}
                                    <Typography
                                        variant="h5"
                                        component="h3"
                                        gutterBottom
                                        sx={{
                                            fontWeight: 600,
                                            color: theme.palette.primary.dark,
                                            mb: 2
                                        }}
                                    >
                                        {step.title}
                                    </Typography>

                                    {/* Description */}
                                    <Typography
                                        variant="body1"
                                        color="text.secondary"
                                        sx={{
                                            lineHeight: 1.7,
                                            fontSize: '1rem'
                                        }}
                                    >
                                        {step.description}
                                    </Typography>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default HowItWorksSection;
