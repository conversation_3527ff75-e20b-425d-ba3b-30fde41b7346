// material-ui
import { Box, Container, Typography, Grid } from '@mui/material';

// SVG Icons from Figma design
const iconRegister = 'http://localhost:3845/assets/c54ebc760ec8934c77d6166a4d0003e09ed8226f.svg';
const iconExplore = 'http://localhost:3845/assets/53b44199e5ea2d124d44cceef359fd8e78a75a7e.svg';
const iconFind = 'http://localhost:3845/assets/79d01b98894b67fbb4720426af40bdf7fd52b34e.svg';

// Custom SVG background component
const CircleBackground = () => (
    <svg width="105" height="113" viewBox="0 0 105 113" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            opacity="0.6"
            d="M84.9245 107.33C110.816 94.9807 108.021 43.2543 96.8406 26.0984C87.6829 12.0484 51.5669 -2.63025 35.1271 0.401605C19.4597 3.28557 -0.326912 23.6951 0.0040869 53.8658C0.371867 83.9995 39.5772 128.923 84.9245 107.33Z"
            fill="#F0F5F7"
        />
    </svg>
);

const steps = [
    {
        id: 1,
        icon: iconRegister,
        title: 'Register an account to start',
        description: 'Đăng ký tài khoản để bắt đầu hành trình tìm kiếm công việc mơ ước của bạn'
    },
    {
        id: 2,
        icon: iconExplore,
        title: 'Explore over thousands of resumes',
        description: 'Khám phá hàng nghìn hồ sơ ứng viên chất lượng cao phù hợp với yêu cầu'
    },
    {
        id: 3,
        icon: iconFind,
        title: 'Find the most suitable candidate',
        description: 'Tìm kiếm và lựa chọn ứng viên phù hợp nhất cho vị trí công việc'
    }
];

const HowItWorksSection = () => {
    return (
        <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: '#FFFFFF' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontFamily: 'Jost, sans-serif',
                            fontWeight: 500,
                            color: '#202124',
                            mb: 2,
                            fontSize: { xs: '24px', md: '30px' },
                            lineHeight: 'normal'
                        }}
                    >
                        How It Works?
                    </Typography>
                    <Typography
                        variant="body1"
                        sx={{
                            fontFamily: 'Jost, sans-serif',
                            fontWeight: 400,
                            color: '#696969',
                            fontSize: '15px',
                            lineHeight: 'normal'
                        }}
                    >
                        Job for anyone, anywhere
                    </Typography>
                </Box>

                {/* Steps Grid */}
                <Grid container spacing={6} justifyContent="center">
                    {steps.map((step) => (
                        <Grid item xs={12} md={4} key={step.id}>
                            <Box
                                sx={{
                                    textAlign: 'center',
                                    position: 'relative'
                                }}
                            >
                                {/* Icon with circular background */}
                                <Box
                                    sx={{
                                        position: 'relative',
                                        display: 'inline-flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: 105,
                                        height: 113,
                                        mb: 4,
                                        mx: 'auto'
                                    }}
                                >
                                    {/* Background circle */}
                                    <Box
                                        sx={{
                                            position: 'absolute',
                                            width: 105,
                                            height: 113,
                                            transform: 'rotate(180deg)'
                                        }}
                                    >
                                        <CircleBackground />
                                    </Box>
                                    {/* Icon */}
                                    <Box
                                        component="img"
                                        src={step.icon}
                                        alt=""
                                        sx={{
                                            position: 'relative',
                                            width: step.id === 1 ? 50 : step.id === 2 ? 60 : 49.688,
                                            height: step.id === 1 ? 60.663 : step.id === 2 ? 60 : 60.284,
                                            zIndex: 1
                                        }}
                                    />
                                </Box>

                                {/* Title */}
                                <Typography
                                    variant="h6"
                                    component="h3"
                                    sx={{
                                        fontFamily: 'Jost, sans-serif',
                                        fontWeight: 500,
                                        color: '#202124',
                                        fontSize: '18px',
                                        lineHeight: 'normal',
                                        mb: 2,
                                        whiteSpace: 'pre-line'
                                    }}
                                >
                                    {step.title}
                                </Typography>

                                {/* Description */}
                                <Typography
                                    variant="body2"
                                    sx={{
                                        fontFamily: 'Jost, sans-serif',
                                        fontWeight: 400,
                                        color: '#696969',
                                        fontSize: '14px',
                                        lineHeight: 1.6,
                                        maxWidth: 280,
                                        mx: 'auto'
                                    }}
                                >
                                    {step.description}
                                </Typography>
                            </Box>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default HowItWorksSection;
