// material-ui
import { Box, Container, Typography, Grid } from '@mui/material';

// SVG Icons from Figma design
const iconRegister = 'http://localhost:3845/assets/c54ebc760ec8934c77d6166a4d0003e09ed8226f.svg';
const iconExplore = 'http://localhost:3845/assets/53b44199e5ea2d124d44cceef359fd8e78a75a7e.svg';
const iconFind = 'http://localhost:3845/assets/79d01b98894b67fbb4720426af40bdf7fd52b34e.svg';
const circleBackground = 'http://localhost:3845/assets/8e1a67341502e7630579ecb83da31a7c12995e81.svg';

const steps = [
    {
        id: 1,
        icon: iconRegister,
        title: 'Register an account to start',
        description: '<PERSON>ăng ký tài khoản để bắt đầu hành trình tìm kiếm công việc mơ <PERSON>ớc của bạn'
    },
    {
        id: 2,
        icon: iconExplore,
        title: 'Explore over thousands of resumes',
        description: 'Khám phá hàng nghìn hồ sơ ứng viên chất lượng cao phù hợp với yêu cầu'
    },
    {
        id: 3,
        icon: iconFind,
        title: 'Find the most suitable candidate',
        description: 'Tìm kiếm và lựa chọn ứng viên phù hợp nhất cho vị trí công việc'
    }
];

const HowItWorksSection = () => {
    return (
        <Box sx={{ py: { xs: 8, md: 12 }, backgroundColor: '#F0F5F7' }}>
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontFamily: 'Jost, sans-serif',
                            fontWeight: 500,
                            color: '#202124',
                            mb: 2,
                            fontSize: { xs: '24px', md: '30px' },
                            lineHeight: 'normal'
                        }}
                    >
                        How It Works?
                    </Typography>
                    <Typography
                        variant="body1"
                        sx={{
                            fontFamily: 'Jost, sans-serif',
                            fontWeight: 400,
                            color: '#696969',
                            fontSize: '15px',
                            lineHeight: 'normal'
                        }}
                    >
                        Job for anyone, anywhere
                    </Typography>
                </Box>

                {/* Steps Grid */}
                <Grid container spacing={6} justifyContent="center">
                    {steps.map((step) => (
                        <Grid item xs={12} md={4} key={step.id}>
                            <Box
                                sx={{
                                    textAlign: 'center',
                                    position: 'relative'
                                }}
                            >
                                {/* Icon with circular background */}
                                <Box
                                    sx={{
                                        position: 'relative',
                                        display: 'inline-flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        width: 105,
                                        height: 113,
                                        mb: 4,
                                        mx: 'auto'
                                    }}
                                >
                                    {/* Background circle */}
                                    <Box
                                        component="img"
                                        src={circleBackground}
                                        alt=""
                                        sx={{
                                            position: 'absolute',
                                            width: 105,
                                            height: 113,
                                            transform: 'rotate(180deg)'
                                        }}
                                    />
                                    {/* Icon */}
                                    <Box
                                        component="img"
                                        src={step.icon}
                                        alt=""
                                        sx={{
                                            position: 'relative',
                                            width: step.id === 1 ? 50 : step.id === 2 ? 60 : 49.688,
                                            height: step.id === 1 ? 60.663 : step.id === 2 ? 60 : 60.284,
                                            zIndex: 1
                                        }}
                                    />
                                </Box>

                                {/* Title */}
                                <Typography
                                    variant="h6"
                                    component="h3"
                                    sx={{
                                        fontFamily: 'Jost, sans-serif',
                                        fontWeight: 500,
                                        color: '#202124',
                                        fontSize: '18px',
                                        lineHeight: 'normal',
                                        mb: 2,
                                        whiteSpace: 'pre-line'
                                    }}
                                >
                                    {step.title}
                                </Typography>

                                {/* Description */}
                                <Typography
                                    variant="body2"
                                    sx={{
                                        fontFamily: 'Jost, sans-serif',
                                        fontWeight: 400,
                                        color: '#696969',
                                        fontSize: '14px',
                                        lineHeight: 1.6,
                                        maxWidth: 280,
                                        mx: 'auto'
                                    }}
                                >
                                    {step.description}
                                </Typography>
                            </Box>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default HowItWorksSection;
