// material-ui
import { Box, Container, Typography, Grid, Card, CardContent, Avatar, useTheme } from '@mui/material';
import { FormatQuote } from '@mui/icons-material';

const testimonialsData = [
    {
        id: 1,
        title: 'Good theme',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        name: '<PERSON>',
        position: 'Web Developer',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b77c?w=150&h=150&fit=crop&crop=face&auto=format',
        featured: false
    },
    {
        id: 2,
        title: 'Great quality!',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        name: '<PERSON>',
        position: 'Consultant',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format',
        featured: true
    },
    {
        id: 3,
        title: 'Awesome Design',
        content:
            "Without JobHunt i'd be homeless, they found me a job and got me sorted out quickly with everything! Can't quite… The Mitech team works really hard to ensure high level of quality",
        name: '<PERSON> <PERSON>',
        position: 'Designer',
        avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face&auto=format',
        featured: false
    }
];

const TestimonialsSection = () => {
    const theme = useTheme();

    return (
        <Box sx={{ py: { xs: 6, md: 10 }, backgroundColor: '#F0F5F7' }}>
            <Container maxWidth="lg">
                {/* Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontWeight: 500,
                            color: theme.palette.primary.dark,
                            mb: 2
                        }}
                    >
                        Testimonials From Our Customers
                    </Typography>
                    <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 400 }}>
                        Lorem ipsum dolor sit amet elit, sed do eiusmod tempor
                    </Typography>
                </Box>

                {/* Testimonials Grid */}
                <Grid container spacing={4} sx={{ mb: 6 }}>
                    {testimonialsData.map((testimonial) => (
                        <Grid item xs={12} md={4} key={testimonial.id}>
                            <Card
                                elevation={testimonial.featured ? 6 : 1}
                                sx={{
                                    height: '100%',
                                    position: 'relative',
                                    p: 3,
                                    opacity: testimonial.featured ? 1 : 0.7,
                                    transform: testimonial.featured ? 'scale(1.02)' : 'scale(1)',
                                    transition: 'all 0.3s ease',
                                    '&:hover': {
                                        opacity: 1,
                                        transform: 'scale(1.02)'
                                    }
                                }}
                            >
                                {/* Quote Icon */}
                                <Box
                                    sx={{
                                        position: 'absolute',
                                        top: 24,
                                        right: 24,
                                        color: '#ECEDF2'
                                    }}
                                >
                                    <FormatQuote sx={{ fontSize: 32 }} />
                                </Box>

                                <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
                                    {/* Title */}
                                    <Typography
                                        variant="h6"
                                        component="h3"
                                        sx={{
                                            color: theme.palette.primary.main,
                                            fontWeight: 500,
                                            mb: 2
                                        }}
                                    >
                                        {testimonial.title}
                                    </Typography>

                                    {/* Content */}
                                    <Typography
                                        variant="body1"
                                        color="text.secondary"
                                        sx={{
                                            mb: 4,
                                            lineHeight: 1.6,
                                            fontSize: '1rem'
                                        }}
                                    >
                                        {testimonial.content}
                                    </Typography>

                                    {/* User Info */}
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                        <Avatar
                                            src={testimonial.avatar}
                                            alt={testimonial.name}
                                            sx={{
                                                width: 60,
                                                height: 60,
                                                border: `2px solid ${theme.palette.divider}`
                                            }}
                                        />
                                        <Box>
                                            <Typography
                                                variant="h6"
                                                component="h4"
                                                sx={{
                                                    fontWeight: 500,
                                                    color: theme.palette.primary.dark,
                                                    mb: 0.5
                                                }}
                                            >
                                                {testimonial.name}
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                {testimonial.position}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>

                {/* Pagination Dots */}
                <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                    {[0, 1, 2, 3].map((index) => (
                        <Box
                            key={index}
                            sx={{
                                width: index === 1 ? 20 : 8,
                                height: 8,
                                borderRadius: index === 1 ? 20 : '50%',
                                backgroundColor: index === 1 ? theme.palette.primary.main : '#BFC8CB',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease'
                            }}
                        />
                    ))}
                </Box>
            </Container>
        </Box>
    );
};

export default TestimonialsSection;
