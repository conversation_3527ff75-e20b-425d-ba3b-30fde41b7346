// material-ui
import { Box, Container, Grid, Typography, Button, useTheme } from '@mui/material';

const RecruitingSection = () => {
    const theme = useTheme();

    return (
        <Box
            sx={{
                py: { xs: 8, md: 12 },
                background: `linear-gradient(135deg, rgba(25, 103, 210, 0.05) 0%, rgba(245, 247, 252, 0.8) 100%)`
            }}
        >
            <Container maxWidth="lg">
                <Grid container spacing={6} alignItems="center">
                    {/* Left Content */}
                    <Grid item xs={12} md={6}>
                        <Box>
                            <Typography
                                variant="h3"
                                component="h2"
                                sx={{
                                    fontWeight: 500,
                                    color: theme.palette.primary.dark,
                                    mb: 3
                                }}
                            >
                                Recruiting?
                            </Typography>

                            <Typography
                                variant="body1"
                                color="text.secondary"
                                sx={{
                                    mb: 4,
                                    lineHeight: 1.7,
                                    fontSize: '1.1rem'
                                }}
                            >
                                Advertise your jobs to millions of monthly users and search 15.8 million CVs in our database.
                            </Typography>

                            {/* CTA Button */}
                            <Button
                                variant="contained"
                                size="large"
                                sx={{
                                    px: 4,
                                    py: 2,
                                    fontSize: '1rem',
                                    textTransform: 'none',
                                    borderRadius: 2,
                                    minWidth: 220
                                }}
                            >
                                Start Recruiting Now
                            </Button>
                        </Box>
                    </Grid>

                    {/* Right Illustration */}
                    <Grid item xs={12} md={6}>
                        <Box
                            sx={{
                                textAlign: 'center',
                                position: 'relative'
                            }}
                        >
                            {/* Background Pattern */}
                            <Box
                                sx={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    right: 0,
                                    bottom: 0,
                                    backgroundColor: `rgba(25, 103, 210, 0.03)`,
                                    borderRadius: 2,
                                    transform: 'rotate(-3deg)',
                                    zIndex: 0
                                }}
                            />

                            {/* SVG Illustration */}
                            <Box
                                component="img"
                                src="src/assets/images/recruiting-illustration.svg"
                                alt="Recruiting Illustration"
                                sx={{
                                    maxWidth: '100%',
                                    height: 'auto',
                                    maxHeight: 400,
                                    position: 'relative',
                                    zIndex: 1
                                }}
                            />
                        </Box>
                    </Grid>
                </Grid>
            </Container>
        </Box>
    );
};

export default RecruitingSection;
