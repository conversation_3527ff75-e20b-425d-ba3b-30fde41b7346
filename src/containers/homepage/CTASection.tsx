// material-ui
import { Box, Container, Typography, Grid, Card, CardContent, Button, useTheme } from '@mui/material';
import { PersonAdd, Business, ArrowForward } from '@mui/icons-material';

interface CTASectionProps {
    isLoggedIn?: boolean;
}

const CTASection = ({ isLoggedIn = false }: CTASectionProps) => {
    const theme = useTheme();

    // Don't render if user is logged in
    if (isLoggedIn) {
        return null;
    }

    const handleCandidateSignup = () => {
        console.log('Navigate to candidate signup');
        // TODO: Navigate to candidate signup page
    };

    const handleEmployerSignup = () => {
        console.log('Navigate to employer signup');
        // TODO: Navigate to employer signup page
    };

    return (
        <Box 
            sx={{ 
                py: { xs: 8, md: 12 },
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                color: 'white'
            }}
        >
            <Container maxWidth="lg">
                {/* Section Header */}
                <Box textAlign="center" mb={8}>
                    <Typography
                        variant="h3"
                        component="h2"
                        gutterBottom
                        sx={{
                            fontWeight: 600,
                            mb: 2,
                            fontSize: { xs: '2rem', md: '2.5rem' }
                        }}
                    >
                        Bắt đầu hành trình của bạn
                    </Typography>
                    <Typography 
                        variant="h6" 
                        sx={{ 
                            fontWeight: 400,
                            opacity: 0.9,
                            maxWidth: 600,
                            mx: 'auto'
                        }}
                    >
                        Tham gia cộng đồng WorkFinder ngay hôm nay và khám phá cơ hội nghề nghiệp tuyệt vời
                    </Typography>
                </Box>

                {/* CTA Cards */}
                <Grid container spacing={4} justifyContent="center">
                    {/* Candidate CTA */}
                    <Grid item xs={12} md={6} lg={5}>
                        <Card
                            elevation={8}
                            sx={{
                                height: '100%',
                                backgroundColor: 'white',
                                color: theme.palette.text.primary,
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                    transform: 'translateY(-8px)',
                                    boxShadow: '0px 24px 48px rgba(0, 0, 0, 0.2)'
                                }
                            }}
                        >
                            <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                {/* Icon */}
                                <Box
                                    sx={{
                                        width: 80,
                                        height: 80,
                                        borderRadius: '50%',
                                        backgroundColor: theme.palette.primary.light,
                                        color: 'white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 3
                                    }}
                                >
                                    <PersonAdd sx={{ fontSize: 40 }} />
                                </Box>

                                {/* Title */}
                                <Typography
                                    variant="h5"
                                    component="h3"
                                    gutterBottom
                                    sx={{
                                        fontWeight: 600,
                                        color: theme.palette.primary.dark,
                                        mb: 2
                                    }}
                                >
                                    Dành cho Ứng viên
                                </Typography>

                                {/* Description */}
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{
                                        mb: 4,
                                        lineHeight: 1.7
                                    }}
                                >
                                    Tìm kiếm công việc mơ ước, tạo hồ sơ chuyên nghiệp và kết nối với các nhà tuyển dụng hàng đầu
                                </Typography>

                                {/* CTA Button */}
                                <Button
                                    variant="contained"
                                    size="large"
                                    endIcon={<ArrowForward />}
                                    onClick={handleCandidateSignup}
                                    sx={{
                                        px: 4,
                                        py: 1.5,
                                        fontSize: '1.1rem',
                                        fontWeight: 600,
                                        textTransform: 'none',
                                        borderRadius: 2,
                                        '&:hover': {
                                            transform: 'scale(1.05)'
                                        }
                                    }}
                                >
                                    Đăng ký Ứng viên
                                </Button>
                            </CardContent>
                        </Card>
                    </Grid>

                    {/* Employer CTA */}
                    <Grid item xs={12} md={6} lg={5}>
                        <Card
                            elevation={8}
                            sx={{
                                height: '100%',
                                backgroundColor: 'white',
                                color: theme.palette.text.primary,
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                    transform: 'translateY(-8px)',
                                    boxShadow: '0px 24px 48px rgba(0, 0, 0, 0.2)'
                                }
                            }}
                        >
                            <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                {/* Icon */}
                                <Box
                                    sx={{
                                        width: 80,
                                        height: 80,
                                        borderRadius: '50%',
                                        backgroundColor: theme.palette.secondary.main,
                                        color: 'white',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 3
                                    }}
                                >
                                    <Business sx={{ fontSize: 40 }} />
                                </Box>

                                {/* Title */}
                                <Typography
                                    variant="h5"
                                    component="h3"
                                    gutterBottom
                                    sx={{
                                        fontWeight: 600,
                                        color: theme.palette.primary.dark,
                                        mb: 2
                                    }}
                                >
                                    Dành cho Nhà tuyển dụng
                                </Typography>

                                {/* Description */}
                                <Typography
                                    variant="body1"
                                    color="text.secondary"
                                    sx={{
                                        mb: 4,
                                        lineHeight: 1.7
                                    }}
                                >
                                    Đăng tin tuyển dụng, tìm kiếm ứng viên tài năng và xây dựng đội ngũ mạnh mẽ cho công ty
                                </Typography>

                                {/* CTA Button */}
                                <Button
                                    variant="contained"
                                    color="secondary"
                                    size="large"
                                    endIcon={<ArrowForward />}
                                    onClick={handleEmployerSignup}
                                    sx={{
                                        px: 4,
                                        py: 1.5,
                                        fontSize: '1.1rem',
                                        fontWeight: 600,
                                        textTransform: 'none',
                                        borderRadius: 2,
                                        '&:hover': {
                                            transform: 'scale(1.05)'
                                        }
                                    }}
                                >
                                    Đăng ký Nhà tuyển dụng
                                </Button>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
        </Box>
    );
};

export default CTASection;
