// material-ui
import { Box, Container, Grid } from '@mui/material';

const companyLogos = [
    {
        id: 1,
        name: 'Company 1',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+1'
    },
    {
        id: 2,
        name: 'Company 2',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+2'
    },
    {
        id: 3,
        name: 'Company 3',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+3'
    },
    {
        id: 4,
        name: 'Company 4',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+4'
    },
    {
        id: 5,
        name: 'Company 5',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+5'
    },
    {
        id: 6,
        name: 'Company 6',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+6'
    },
    {
        id: 7,
        name: 'Company 7',
        logo: 'https://via.placeholder.com/120x40/E5EBF5/696969?text=Company+7'
    }
];

const CompanyLogosSection = () => {
    return (
        <Box sx={{ py: { xs: 4, md: 6 } }}>
            <Container maxWidth="lg">
                {/* Divider Line */}
                <Box
                    sx={{
                        width: '100%',
                        height: 1,
                        backgroundColor: '#ECEDF2',
                        mb: { xs: 4, md: 6 }
                    }}
                />

                {/* Company Logos Grid */}
                <Grid container spacing={4} alignItems="center" justifyContent="center">
                    {companyLogos.map((company) => (
                        <Grid
                            item
                            xs={6}
                            sm={4}
                            md={3}
                            lg={2}
                            key={company.id}
                            sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}
                        >
                            <Box
                                component="img"
                                src={company.logo}
                                alt={company.name}
                                sx={{
                                    maxWidth: '100%',
                                    height: 'auto',
                                    maxHeight: 50,
                                    opacity: 0.6,
                                    transition: 'opacity 0.3s ease',
                                    '&:hover': {
                                        opacity: 1
                                    }
                                }}
                            />
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default CompanyLogosSection;
