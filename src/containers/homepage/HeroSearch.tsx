// material-ui
import { Box, Container, Typography, Grid, useTheme, alpha } from '@mui/material';

// assets
import heroRightImage from '../../assets/images/home/<USER>';
import vectorBackground from '../../assets/images/home/<USER>';

// project imports
import { IHomepageSearchConfig, popularSearches } from 'pages/homepage/Config';
import HeroSearchForm from './HeroSearchForm';

const HeroSearch = () => {
    const theme = useTheme();

    const handleSearch = (values: IHomepageSearchConfig) => {
        console.log('Search values:', values);
        // TODO: Implement search functionality
    };

    return (
        <Box
            sx={{
                background: `linear-gradient(135deg, #F5F7FC 0%, #F5F7FC 100%)`,
                minHeight: { xs: '80vh', md: '100vh' },
                display: 'flex',
                alignItems: 'center',
                position: 'relative',
                overflow: 'hidden',
                paddingTop: { xs: 0, md: 0 }
            }}
        >
            {/* Vector Background - Mobile Only */}
            <Box
                sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '110%',
                    height: '85%',
                    background: `url(${vectorBackground}) no-repeat center`,
                    backgroundSize: 'contain',
                    zIndex: 1,
                    display: { xs: 'block', md: 'none' }
                }}
            />

            <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
                <Grid container spacing={6} alignItems="center">
                    {/* Left Content */}
                    <Grid item xs={12} lg={6}>
                        <Box sx={{ textAlign: { xs: 'center', lg: 'left' } }}>
                            {/* Main Title */}
                            <Typography
                                variant="h1"
                                sx={{
                                    fontSize: { xs: '1.75rem', sm: '2.25rem', md: '3.5rem', lg: '4rem' },
                                    fontWeight: 500,
                                    color: '#202124',
                                    lineHeight: 1.445,
                                    mb: { xs: 1.5, md: 2 },
                                    px: { xs: 2, sm: 0 }
                                }}
                            >
                                There Are{' '}
                                <Box component="span" sx={{ color: theme.palette.primary.main }}>
                                    93,178
                                </Box>{' '}
                                Postings Here For you!
                            </Typography>

                            {/* Subtitle */}
                            <Typography
                                variant="h6"
                                sx={{
                                    color: '#696969',
                                    mb: { xs: 3, md: 4 },
                                    fontSize: { xs: '0.9rem', md: '1.1rem' },
                                    fontWeight: 400,
                                    px: { xs: 2, sm: 0 }
                                }}
                            >
                                Find Jobs, Employment & Career Opportunities
                            </Typography>

                            {/* Search Form */}
                            <Box sx={{ mb: { xs: 2, md: 3 }, px: { xs: 1, sm: 0 } }}>
                                <HeroSearchForm handleSearch={handleSearch} />
                            </Box>

                            {/* Popular Searches */}
                            <Box sx={{ px: { xs: 2, sm: 0 } }}>
                                <Typography
                                    variant="body1"
                                    component="span"
                                    sx={{
                                        color: '#202124',
                                        fontWeight: 400,
                                        fontSize: 15,
                                        mr: 1
                                    }}
                                >
                                    Popular Searches:
                                </Typography>
                                {popularSearches.map((search, index) => (
                                    <Typography
                                        key={index}
                                        variant="body1"
                                        component="span"
                                        sx={{
                                            color: '#696969',
                                            fontSize: 15,
                                            fontWeight: 400,
                                            cursor: 'pointer',
                                            '&:hover': {
                                                color: theme.palette.primary.main
                                            },
                                            '&:not(:last-child)::after': {
                                                content: '", "',
                                                color: '#696969'
                                            }
                                        }}
                                    >
                                        {search}
                                    </Typography>
                                ))}
                            </Box>
                        </Box>
                    </Grid>

                    {/* Right Content - Hero Image - Hidden on mobile */}
                    <Grid item xs={12} lg={6} sx={{ display: { xs: 'none', lg: 'block' } }}>
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                height: { md: 500 },
                                position: 'relative'
                            }}
                        >
                            <Box
                                component="img"
                                src={heroRightImage}
                                alt="Find your dream job"
                                sx={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'contain',
                                    maxWidth: { md: '450px', lg: '500px' },
                                    maxHeight: { md: '400px', lg: '450px' }
                                }}
                            />
                        </Box>
                    </Grid>
                </Grid>
            </Container>
        </Box>
    );
};

export default HeroSearch;
