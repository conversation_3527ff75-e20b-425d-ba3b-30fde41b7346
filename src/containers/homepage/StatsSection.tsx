// material-ui
import { Box, Container, Typography, Grid, useTheme, alpha } from '@mui/material';
import { TrendingUpOutlined as TrendingUpIcon, WorkOutlined as WorkIcon, ForumOutlined as ForumIcon } from '@mui/icons-material';

// project imports
import { siteStats, ISiteStats } from 'pages/homepage/Config';

const StatsSection = () => {
    const theme = useTheme();

    // Icon mapping
    const iconMapping: { [key: string]: any } = {
        TrendingUp: TrendingUpIcon,
        Work: WorkIcon,
        Forum: ForumIcon
    };

    const getIconComponent = (iconName: string) => {
        const IconComponent = iconMapping[iconName];
        return IconComponent ? <IconComponent /> : <TrendingUpIcon />;
    };

    return (
        <Box
            sx={{
                py: { xs: 6, md: 10 },
                backgroundColor: theme.palette.primary.main,
                color: theme.palette.primary.contrastText
            }}
        >
            <Container maxWidth="lg">
                <Grid container spacing={4}>
                    {siteStats.map((stat, index) => (
                        <Grid item xs={12} md={4} key={stat.id}>
                            <Box
                                textAlign="center"
                                sx={{
                                    position: 'relative',
                                    py: 2,
                                    // Add divider for desktop view
                                    '&:not(:last-child)': {
                                        '&::after': {
                                            content: '""',
                                            position: 'absolute',
                                            right: 0,
                                            top: '50%',
                                            transform: 'translateY(-50%)',
                                            width: 1,
                                            height: '60%',
                                            backgroundColor: alpha(theme.palette.primary.contrastText, 0.2),
                                            display: { xs: 'none', md: 'block' }
                                        }
                                    }
                                }}
                            >
                                {/* Icon */}
                                <Box
                                    sx={{
                                        width: 80,
                                        height: 80,
                                        borderRadius: '50%',
                                        backgroundColor: alpha(theme.palette.primary.contrastText, 0.1),
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mx: 'auto',
                                        mb: 3
                                    }}
                                >
                                    <Box
                                        sx={{
                                            color: theme.palette.primary.contrastText,
                                            fontSize: '2rem'
                                        }}
                                    >
                                        {getIconComponent(stat.icon)}
                                    </Box>
                                </Box>

                                {/* Value */}
                                <Typography
                                    variant="h2"
                                    sx={{
                                        fontSize: { xs: '2.5rem', md: '3rem' },
                                        fontWeight: 700,
                                        color: theme.palette.primary.contrastText,
                                        mb: 1
                                    }}
                                >
                                    {stat.value}
                                </Typography>

                                {/* Label */}
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: '1.1rem',
                                        color: alpha(theme.palette.primary.contrastText, 0.8),
                                        fontWeight: 400,
                                        lineHeight: 1.4
                                    }}
                                >
                                    {stat.label}
                                </Typography>
                            </Box>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </Box>
    );
};

export default StatsSection;
