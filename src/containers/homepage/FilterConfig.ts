import { IFilterType } from 'types/job';

// Filter data configuration
export const filterTypesData: IFilterType[] = [
    {
        id: 'salary',
        label: '<PERSON><PERSON><PERSON> l<PERSON>',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'under-10', label: 'Dưới 10 triệu' },
            { value: '10-15', label: 'Từ 10-15 triệu' },
            { value: '15-20', label: 'Từ 15-20 triệu' },
            { value: '20-25', label: 'Từ 20-25 triệu' },
            { value: '25-30', label: 'Từ 25-30 triệu' },
            { value: 'above-30', label: 'Trên 30 triệu' }
        ]
    },
    {
        id: 'location',
        label: 'Địa điểm',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'hanoi', label: 'Hà Nội' },
            { value: 'hcm', label: '<PERSON><PERSON>' },
            { value: 'danang', label: 'Đà Nẵng' },
            { value: 'haiphong', label: '<PERSON><PERSON><PERSON>' },
            { value: 'cantho', label: '<PERSON>ần <PERSON>h<PERSON>' },
            { value: 'remote', label: 'Remote' }
        ]
    },
    {
        id: 'experience',
        label: 'Kinh nghiệm',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'intern', label: 'Thực tập sinh' },
            { value: 'fresher', label: 'Fresher (0-1 năm)' },
            { value: 'junior', label: 'Junior (1-3 năm)' },
            { value: 'middle', label: 'Middle (3-5 năm)' },
            { value: 'senior', label: 'Senior (5+ năm)' },
            { value: 'lead', label: 'Lead/Manager' }
        ]
    },
    {
        id: 'jobType',
        label: 'Loại hình',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'fulltime', label: 'Toàn thời gian' },
            { value: 'parttime', label: 'Bán thời gian' },
            { value: 'contract', label: 'Hợp đồng' },
            { value: 'freelance', label: 'Freelance' },
            { value: 'internship', label: 'Thực tập' }
        ]
    }
];

// Default filter state
export const defaultFilter = {
    filterType: 'salary',
    filterValue: 'all'
};
