import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// yup
// @ts-ignore - TypeScript module resolution issue with @hookform/resolvers
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// material-ui
import { LoadingButton } from '@mui/lab';
import { IconButton, InputAdornment, Stack, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

// project imports
import { authSelector } from 'store/slice/authSlice';
import { useAppSelector } from 'app/hooks';
import { FormProvider, Input, Checkbox } from 'components/extended/Form';
import { ROUTER } from 'constants/Routers';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

// Form validation schema
const registerSchema = yup.object({
    firstName: yup.string().required('First name is required'),
    lastName: yup.string().required('Last name is required'),
    email: yup.string().email('Invalid email format').required('Email is required'),
    password: yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
    confirmPassword: yup
        .string()
        .oneOf([yup.ref('password')], 'Passwords must match')
        .required('Confirm password is required'),
    agreeTerms: yup.boolean().oneOf([true], 'You must agree to terms and conditions')
});

interface IRegisterConfig {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    agreeTerms: boolean;
}

const registerConfig: IRegisterConfig = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false
};

// ============================|| REGISTER ||============================ //

const AuthRegister = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const { loading } = useAppSelector(authSelector);
    const theme = useTheme();
    const navigate = useNavigate();

    const handleSubmit = async (values: IRegisterConfig) => {
        console.log('Register form submitted:', values);
        // TODO: Implement register logic
    };

    const handleClickShowPassword = () => {
        setShowPassword(!showPassword);
    };

    const handleClickShowConfirmPassword = () => {
        setShowConfirmPassword(!showConfirmPassword);
    };

    return (
        <FormProvider
            form={{
                defaultValues: registerConfig,
                resolver: yupResolver(registerSchema)
            }}
            onSubmit={handleSubmit}
        >
            <Box sx={{ width: '100%' }}>
                <Stack spacing={3}>
                    {/* Name Fields */}
                    <Stack direction="row" spacing={2}>
                        <Stack spacing={1} sx={{ flex: 1 }}>
                            <Typography
                                variant="body2"
                                sx={{
                                    fontSize: 14,
                                    fontWeight: 600,
                                    color: theme.palette.text.primary,
                                    mb: 0.5
                                }}
                            >
                                First Name
                            </Typography>
                            <Input
                                name="firstName"
                                placeholder="John"
                                textFieldProps={{
                                    size: 'medium',
                                    autoComplete: 'given-name',
                                    fullWidth: true,
                                    sx: {
                                        '& .MuiOutlinedInput-root': {
                                            borderRadius: 1,
                                            fontSize: 14,
                                            '& .MuiInputBase-input::placeholder': {
                                                color: theme.palette.grey[400],
                                                opacity: 1
                                            }
                                        }
                                    }
                                }}
                            />
                        </Stack>

                        <Stack spacing={1} sx={{ flex: 1 }}>
                            <Typography
                                variant="body2"
                                sx={{
                                    fontSize: 14,
                                    fontWeight: 600,
                                    color: theme.palette.text.primary,
                                    mb: 0.5
                                }}
                            >
                                Last Name
                            </Typography>
                            <Input
                                name="lastName"
                                placeholder="Doe"
                                textFieldProps={{
                                    size: 'medium',
                                    autoComplete: 'family-name',
                                    fullWidth: true,
                                    sx: {
                                        '& .MuiOutlinedInput-root': {
                                            borderRadius: 1,
                                            fontSize: 14,
                                            '& .MuiInputBase-input::placeholder': {
                                                color: theme.palette.grey[400],
                                                opacity: 1
                                            }
                                        }
                                    }
                                }}
                            />
                        </Stack>
                    </Stack>

                    {/* Email Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Email
                        </Typography>
                        <Input
                            name="email"
                            placeholder="<EMAIL>"
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'email',
                                fullWidth: true,
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Password Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Password
                        </Typography>
                        <Input
                            name="password"
                            placeholder="*****************"
                            type={showPassword ? 'text' : 'password'}
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'new-password',
                                fullWidth: true,
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle password visibility"
                                                onClick={handleClickShowPassword}
                                                edge="end"
                                                size="medium"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                },
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Confirm Password Field */}
                    <Stack spacing={1}>
                        <Typography
                            variant="body2"
                            sx={{
                                fontSize: 14,
                                fontWeight: 600,
                                color: theme.palette.text.primary,
                                mb: 0.5
                            }}
                        >
                            Confirm Password
                        </Typography>
                        <Input
                            name="confirmPassword"
                            placeholder="*****************"
                            type={showConfirmPassword ? 'text' : 'password'}
                            textFieldProps={{
                                size: 'medium',
                                autoComplete: 'new-password',
                                fullWidth: true,
                                InputProps: {
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                aria-label="toggle confirm password visibility"
                                                onClick={handleClickShowConfirmPassword}
                                                edge="end"
                                                size="medium"
                                            >
                                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                },
                                sx: {
                                    '& .MuiOutlinedInput-root': {
                                        borderRadius: 1,
                                        fontSize: 14,
                                        '& .MuiInputBase-input::placeholder': {
                                            color: theme.palette.grey[400],
                                            opacity: 1
                                        }
                                    }
                                }
                            }}
                        />
                    </Stack>

                    {/* Terms and Conditions */}
                    <Checkbox
                        name="agreeTerms"
                        label="I agree to the terms and conditions"
                        checkboxProps={{
                            size: 'small',
                            sx: {
                                '& .MuiSvgIcon-root': {
                                    fontSize: 16
                                }
                            }
                        }}
                    />

                    {/* Register Button */}
                    <LoadingButton
                        fullWidth
                        loading={loading?.register}
                        variant="contained"
                        type="submit"
                        size="large"
                        sx={{
                            py: 1.5,
                            borderRadius: 1.5,
                            textTransform: 'none',
                            fontSize: 18,
                            fontWeight: 800,
                            backgroundColor: theme.palette.primary.main,
                            color: 'white',
                            '&:hover': {
                                backgroundColor: theme.palette.primary.dark
                            }
                        }}
                    >
                        Create Account
                    </LoadingButton>
                </Stack>
            </Box>
        </FormProvider>
    );
};

export default AuthRegister;
