// material-ui
import { Box } from '@mui/material';

// project import
import MainCard, { MainCardProps } from 'components/cards/MainCard';

// ==============================|| AUTHENTICATION CARD WRAPPER ||============================== //

interface IProps extends MainCardProps {
    maxWidth?: number;
}

export const AuthCardWrapper = ({ children, maxWidth = 400, ...other }: IProps) => (
    <MainCard
        sx={{
            maxWidth: { xs: maxWidth, lg: maxWidth },
            margin: 3,
            '& > *': {
                flexGrow: 1,
                flexBasis: '50%'
            },
            background: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(20px);'
        }}
        content={false}
        {...other}
    >
        <Box sx={{ p: { xs: 2, sm: 3, xl: 5 } }}>{children}</Box>
    </MainCard>
);

// Export Authentication Components
export { default as AuthLogin } from './AuthLogin';
export { default as AuthRegister } from './AuthRegister';
export { default as AuthForgotPassword } from './AuthForgotPassword';

export default AuthCardWrapper;
