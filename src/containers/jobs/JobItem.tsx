import React from 'react';
import { Box, Typography, IconButton, Avatar, useTheme, alpha, Chip } from '@mui/material';
import {
    BusinessOutlined as CompanyIcon,
    BookmarkBorderOutlined as BookmarkIcon,
    BookmarkOutlined as BookmarkFilledIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'components/cards/MainCard';
import { IJobItemProps } from 'types/job';

const JobItem: React.FC<IJobItemProps> = ({
    job,
    variant = 'default',
    showBookmark = true,
    showTags = true,
    showDetails = true,
    showApplicationButton = false,
    onJobClick,
    onBookmarkClick,
    onApplyClick,
    sx = {}
}) => {
    const theme = useTheme();

    const handleJobClick = () => {
        if (onJobClick) {
            onJobClick(job);
        }
    };

    const handleBookmarkClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        if (onBookmarkClick) {
            onBookmarkClick(job, event);
        }
    };

    const getCardSx = () => {
        return {
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            borderRadius: 2,
            position: 'relative',
            border: '1px solid',
            borderColor: 'grey.200',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            backgroundColor: 'background.paper',
            '&:hover': {
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
                borderColor: 'grey.300'
            }
        };
    };

    const jobContent = (
        <Box position="relative" p={2}>
            {/* Top Section - Logo + Job Info */}
            <Box display="flex" gap={2} mb={2}>
                {/* Company Logo - Large Square */}
                <Avatar
                    src={job.logo || job.companyLogo}
                    variant="rounded"
                    sx={{
                        width: 64,
                        height: 64,
                        backgroundColor: theme.palette.grey[100],
                        color: theme.palette.grey[600],
                        flexShrink: 0,
                        borderRadius: 1.5
                    }}
                >
                    <CompanyIcon fontSize="large" />
                </Avatar>

                {/* Job Info - Natural flow */}
                <Box flex={1} minWidth={0} display="flex" flexDirection="column" justifyContent="center">
                    {/* Job Title */}
                    <Typography
                        variant="h6"
                        sx={{
                            fontWeight: 600,
                            fontSize: '1rem',
                            color: theme.palette.text.primary,
                            lineHeight: 1.3,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            wordBreak: 'break-word',
                            textOverflow: 'ellipsis',
                            mb: 0.5
                        }}
                    >
                        {job.title}
                    </Typography>

                    {/* Company Name */}
                    <Typography
                        variant="body2"
                        sx={{
                            color: theme.palette.text.secondary,
                            fontSize: '0.875rem',
                            fontWeight: 500,
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            wordBreak: 'break-word',
                            textOverflow: 'ellipsis',
                            width: '100%'
                        }}
                    >
                        {job.company}
                    </Typography>
                </Box>
            </Box>

            {/* Bottom Section - Salary and Location (aligned below logo) */}
            <Box display="flex" alignItems="center" gap={1} flexWrap="wrap" pl={0}>
                {/* Salary Chip */}
                <Chip
                    label={job.salary}
                    size="small"
                    sx={{
                        backgroundColor: theme.palette.grey[200],
                        color: theme.palette.text.primary,
                        fontWeight: 500,
                        fontSize: '0.75rem',
                        height: 28,
                        '& .MuiChip-label': {
                            px: 1.5,
                            color: theme.palette.text.primary
                        }
                    }}
                />

                {/* Location Chip */}
                <Chip
                    label={job.location}
                    size="small"
                    sx={{
                        backgroundColor: theme.palette.grey[200],
                        color: theme.palette.text.primary,
                        fontWeight: 500,
                        fontSize: '0.75rem',
                        height: 28,
                        '& .MuiChip-label': {
                            px: 1.5,
                            color: theme.palette.text.primary
                        }
                    }}
                />
            </Box>

            {/* Bookmark Button - Bottom Right Corner */}
            {showBookmark && (
                <Box position="absolute" bottom={12} right={12} zIndex={1}>
                    <IconButton
                        size="small"
                        sx={{
                            color: job.isBookmarked ? theme.palette.text.primary : theme.palette.grey[500],
                            backgroundColor: 'transparent',
                            padding: '6px',
                            borderRadius: '50%',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: theme.palette.grey[200],
                                color: theme.palette.text.primary
                            }
                        }}
                        onClick={handleBookmarkClick}
                        aria-label="Bookmark công việc"
                    >
                        {job.isBookmarked ? <BookmarkFilledIcon fontSize="small" /> : <BookmarkIcon fontSize="small" />}
                    </IconButton>
                </Box>
            )}
        </Box>
    );

    return (
        <MainCard
            content={false}
            sx={{
                ...getCardSx(),
                ...sx
            }}
            onClick={handleJobClick}
        >
            {jobContent}
        </MainCard>
    );
};

export default JobItem;
