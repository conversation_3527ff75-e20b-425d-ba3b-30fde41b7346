import React from 'react';
import { Box, Skeleton } from '@mui/material';

// project imports
import MainCard from 'components/cards/MainCard';

interface CompanyItemSkeletonProps {
    variant?: 'default' | 'compact' | 'featured' | 'list';
    sx?: object;
}

const CompanyItemSkeleton: React.FC<CompanyItemSkeletonProps> = ({ variant = 'default', sx = {} }) => {
    const getSkeletonSizes = () => {
        switch (variant) {
            case 'compact':
                return { avatar: 40, padding: 2 };
            case 'list':
                return { avatar: 60, padding: 3 };
            default:
                return { avatar: 64, padding: 2 };
        }
    };

    const sizes = getSkeletonSizes();

    const skeletonContent = (
        <Box p={sizes.padding}>
            <Box display="flex" gap={2} mb={2}>
                {/* Company Logo Skeleton */}
                <Skeleton 
                    variant="rounded" 
                    width={sizes.avatar} 
                    height={sizes.avatar} 
                    sx={{ 
                        flexShrink: 0,
                        borderRadius: 1.5
                    }} 
                />

                {/* Company Info Skeleton */}
                <Box flex={1} minWidth={0}>
                    {/* Company Name */}
                    <Skeleton 
                        variant="text" 
                        width="85%" 
                        height={variant === 'compact' ? 24 : 28} 
                        sx={{ mb: 0.5 }} 
                    />

                    {/* Industry */}
                    <Skeleton 
                        variant="text" 
                        width="65%" 
                        height={20} 
                        sx={{ mb: 1 }} 
                    />

                    {/* Company Details */}
                    <Box display="flex" gap={2} mb={1} flexWrap="wrap">
                        <Skeleton variant="text" width={80} height={16} />
                        <Skeleton variant="text" width={60} height={16} />
                    </Box>
                </Box>
            </Box>

            {/* Bottom Section - Chips */}
            <Box display="flex" gap={1} mb={variant === 'compact' ? 0 : 1}>
                <Skeleton variant="rounded" width={120} height={28} />
                <Skeleton variant="rounded" width={80} height={28} />
            </Box>
        </Box>
    );

    return (
        <MainCard
            content={false}
            sx={{
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'grey.200',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                backgroundColor: 'background.paper',
                ...sx
            }}
        >
            {skeletonContent}
        </MainCard>
    );
};

export default CompanyItemSkeleton;
