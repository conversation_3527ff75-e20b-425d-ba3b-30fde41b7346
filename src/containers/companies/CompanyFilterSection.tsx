import React, { useState, useMemo } from 'react';
import { Box, FormControl, Select, MenuItem, InputLabel, SelectChangeEvent, Tabs, Tab, useTheme, useMediaQuery } from '@mui/material';

// project imports
import { ICompanyDynamicFilter, ICompanyFilterType } from 'types/company';
import { companyFilterTypesData, defaultCompanyFilter } from './CompanyFilterConfig';

interface ICompanyFilterSectionProps {
    onFilterChange?: (filter: ICompanyDynamicFilter) => void;
}

const CompanyFilterSection: React.FC<ICompanyFilterSectionProps> = ({ onFilterChange }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));
    const [selectedFilter, setSelectedFilter] = useState<ICompanyDynamicFilter>(defaultCompanyFilter);

    // Get current filter type data
    const currentFilterType = useMemo(() => {
        return companyFilterTypesData.find(type => type.id === selectedFilter.filterType);
    }, [selectedFilter.filterType]);

    // Handle filter type change
    const handleFilterTypeChange = (event: SelectChangeEvent<string>) => {
        const newFilterType = event.target.value;
        const newFilter: ICompanyDynamicFilter = {
            filterType: newFilterType,
            filterValue: 'all' // Reset to "Tất cả" when changing type
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    // Handle filter value change (dropdown)
    const handleFilterValueChange = (event: SelectChangeEvent<string>) => {
        const newFilter: ICompanyDynamicFilter = {
            ...selectedFilter,
            filterValue: event.target.value
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    // Handle tab change (desktop)
    const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
        const newFilter: ICompanyDynamicFilter = {
            ...selectedFilter,
            filterValue: newValue
        };

        setSelectedFilter(newFilter);
        onFilterChange?.(newFilter);
    };

    return (
        <Box
            sx={{
                display: 'flex',
                gap: { xs: 2, sm: 2, md: 3 },
                flexDirection: { xs: 'column', sm: isMobile ? 'column' : 'row' },
                alignItems: { xs: 'stretch', sm: isMobile ? 'stretch' : 'center' },
                justifyContent: { xs: 'stretch', sm: 'flex-start' },
                mb: { xs: 3, md: 4 },
                px: { xs: 1, sm: 2, md: 2 },
                py: { xs: 0, md: 2 },
                width: '100%'
            }}
        >
            {/* Filter Type Selector */}
            <FormControl
                size="medium"
                sx={{
                    minWidth: { xs: '100%', sm: isMobile ? '100%' : 200 },
                    flexShrink: 0
                }}
            >
                <InputLabel sx={{ fontWeight: 500 }}>Lọc theo</InputLabel>
                <Select
                    value={selectedFilter.filterType}
                    onChange={handleFilterTypeChange}
                    label="Lọc theo"
                    sx={{
                        '& .MuiSelect-select': {
                            fontWeight: 500
                        }
                    }}
                >
                    {companyFilterTypesData.map((filterType) => (
                        <MenuItem key={filterType.id} value={filterType.id} sx={{ py: 1.5 }}>
                            {filterType.label}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {/* Filter Value Selector */}
            {isMobile ? (
                /* Mobile: Dropdown */
                <FormControl
                    size="medium"
                    sx={{
                        minWidth: '100%',
                        flexGrow: 1
                    }}
                >
                    <InputLabel sx={{ fontWeight: 500 }}>{currentFilterType?.label || 'Lọc'}</InputLabel>
                    <Select
                        value={selectedFilter.filterValue}
                        onChange={handleFilterValueChange}
                        label={currentFilterType?.label || 'Lọc'}
                        sx={{
                            '& .MuiSelect-select': {
                                fontWeight: 500
                            }
                        }}
                    >
                        {currentFilterType?.options.map((option) => (
                            <MenuItem key={option.value} value={option.value} sx={{ py: 1.5 }}>
                                {option.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            ) : (
                /* Desktop: Custom Pill-style Tabs */
                <Box
                    sx={{
                        flexGrow: 1,
                        display: 'flex',
                        alignItems: 'center',
                        overflow: 'hidden',
                        position: 'relative'
                    }}
                >
                    <Tabs
                        value={selectedFilter.filterValue}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons="auto"
                        allowScrollButtonsMobile
                        sx={{
                            minHeight: 'auto',
                            '& .MuiTabs-flexContainer': {
                                gap: 1
                            },
                            '& .MuiTab-root': {
                                minHeight: 40,
                                minWidth: 'auto',
                                px: 2.5,
                                py: 1,
                                borderRadius: 20,
                                textTransform: 'none',
                                fontWeight: 500,
                                fontSize: '0.875rem',
                                color: theme.palette.text.secondary,
                                backgroundColor: 'transparent',
                                border: '1px solid',
                                borderColor: theme.palette.divider,
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    backgroundColor: theme.palette.action.hover,
                                    borderColor: theme.palette.primary.light
                                },
                                '&.Mui-selected': {
                                    color: theme.palette.primary.contrastText,
                                    backgroundColor: theme.palette.primary.main,
                                    borderColor: theme.palette.primary.main,
                                    '&:hover': {
                                        backgroundColor: theme.palette.primary.dark,
                                        borderColor: theme.palette.primary.dark
                                    }
                                }
                            },
                            '& .MuiTabs-indicator': {
                                display: 'none'
                            },
                            '& .MuiTabs-scrollButtons': {
                                color: theme.palette.text.secondary,
                                '&.Mui-disabled': {
                                    opacity: 0.3
                                }
                            }
                        }}
                    >
                        {currentFilterType?.options.map((option) => (
                            <Tab
                                key={option.value}
                                label={option.label}
                                value={option.value}
                            />
                        ))}
                    </Tabs>
                </Box>
            )}
        </Box>
    );
};

export default CompanyFilterSection;
