import { ICompanyFilterType } from 'types/company';

// Company filter data configuration
export const companyFilterTypesData: ICompanyFilterType[] = [
    {
        id: 'industry',
        label: 'Ngành nghề',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'technology', label: 'Công nghệ thông tin' },
            { value: 'finance', label: 'Tài chính - Ngân hàng' },
            { value: 'healthcare', label: 'Y tế - Sức khỏe' },
            { value: 'education', label: 'Giáo dục - Đào tạo' },
            { value: 'manufacturing', label: 'Sản xuất - Chế tạo' },
            { value: 'retail', label: 'Bán lẻ - Thương mại' },
            { value: 'consulting', label: 'Tư vấn' },
            { value: 'media', label: 'Truyền thông - Quảng cáo' },
            { value: 'real-estate', label: 'Bất động sản' },
            { value: 'logistics', label: 'Vận tải - Logistics' }
        ]
    },
    {
        id: 'location',
        label: 'Địa điểm',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'hanoi', label: 'Hà Nội' },
            { value: 'hcm', label: 'Hồ Chí Minh' },
            { value: 'danang', label: 'Đà Nẵng' },
            { value: 'haiphong', label: 'Hải Phòng' },
            { value: 'cantho', label: 'Cần Thơ' },
            { value: 'binhduong', label: 'Bình Dương' },
            { value: 'dongnai', label: 'Đồng Nai' },
            { value: 'other', label: 'Khác' }
        ]
    },
    {
        id: 'companySize',
        label: 'Quy mô công ty',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'startup', label: 'Startup (1-50 người)' },
            { value: 'small', label: 'Nhỏ (51-200 người)' },
            { value: 'medium', label: 'Trung bình (201-1000 người)' },
            { value: 'large', label: 'Lớn (1000+ người)' },
            { value: 'enterprise', label: 'Tập đoàn (5000+ người)' }
        ]
    },
    {
        id: 'openings',
        label: 'Vị trí tuyển dụng',
        options: [
            { value: 'all', label: 'Tất cả' },
            { value: 'has-openings', label: 'Đang tuyển dụng' },
            { value: '1-5', label: '1-5 vị trí' },
            { value: '6-10', label: '6-10 vị trí' },
            { value: '11-20', label: '11-20 vị trí' },
            { value: '20+', label: 'Trên 20 vị trí' }
        ]
    }
];

// Default company filter state
export const defaultCompanyFilter = {
    filterType: 'industry',
    filterValue: 'all'
};
