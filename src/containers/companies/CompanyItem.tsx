import React from 'react';
import { Box, Typography, IconButton, Avatar, useTheme, Chip } from '@mui/material';
import {
    BusinessOutlined as CompanyIcon,
    FavoriteBorderOutlined as FollowIcon,
    FavoriteOutlined as FollowFilledIcon,
    WorkOutlineOutlined as JobIcon
} from '@mui/icons-material';

// project imports
import MainCard from 'components/cards/MainCard';
import { ICompanyItemProps } from 'types/company';

const CompanyItem: React.FC<ICompanyItemProps> = ({
    company,
    variant = 'default',
    showFollow = true,
    showDetails = true,
    onCompanyClick,
    onFollowClick,
    sx = {}
}) => {
    const theme = useTheme();

    const handleCompanyClick = () => {
        if (onCompanyClick) {
            onCompanyClick(company);
        }
    };

    const handleFollowClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        if (onFollowClick) {
            onFollowClick(company, event);
        }
    };

    const getCardSx = () => {
        return {
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            borderRadius: 2,
            position: 'relative',
            border: '1px solid',
            borderColor: 'grey.200',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
            backgroundColor: 'background.paper',
            '&:hover': {
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
                borderColor: 'grey.300'
            }
        };
    };

    const companyContent = (
        <Box position="relative" p={2}>
            {/* Top Section - Logo + Company Info */}
            <Box display="flex" gap={2} mb={2}>
                {/* Company Logo - Large Square */}
                <Avatar
                    src={company.logo}
                    variant="rounded"
                    sx={{
                        width: 64,
                        height: 64,
                        backgroundColor: theme.palette.grey[100],
                        color: theme.palette.grey[600],
                        flexShrink: 0,
                        borderRadius: 1.5
                    }}
                >
                    <CompanyIcon fontSize="large" />
                </Avatar>

                {/* Company Info */}
                <Box flex={1} minWidth={0}>
                    {/* Company Name */}
                    <Typography
                        variant="h6"
                        component="h3"
                        sx={{
                            fontWeight: 600,
                            fontSize: '1.1rem',
                            color: theme.palette.text.primary,
                            mb: 0.5,
                            lineHeight: 1.3,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical'
                        }}
                    >
                        {company.name}
                    </Typography>

                    {/* Industry */}
                    <Typography
                        variant="body2"
                        sx={{
                            color: theme.palette.text.secondary,
                            fontSize: '0.875rem',
                            mb: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                        }}
                    >
                        {company.industry}
                    </Typography>

                    {/* Company Details */}
                    {showDetails && (
                        <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
                            <Typography
                                variant="caption"
                                sx={{
                                    color: theme.palette.text.secondary,
                                    fontSize: '0.75rem'
                                }}
                            >
                                {company.location}
                            </Typography>
                            {company.size && (
                                <>
                                    <Typography
                                        variant="caption"
                                        sx={{
                                            color: theme.palette.text.secondary,
                                            fontSize: '0.75rem'
                                        }}
                                    >
                                        •
                                    </Typography>
                                    <Typography
                                        variant="caption"
                                        sx={{
                                            color: theme.palette.text.secondary,
                                            fontSize: '0.75rem'
                                        }}
                                    >
                                        {company.size}
                                    </Typography>
                                </>
                            )}
                        </Box>
                    )}
                </Box>
            </Box>

            {/* Bottom Section - Open Positions and Location (aligned below logo) */}
            <Box display="flex" alignItems="center" gap={1} flexWrap="wrap" pl={0}>
                {/* Open Positions Chip */}
                <Chip
                    icon={<JobIcon sx={{ fontSize: '0.875rem !important' }} />}
                    label={`${company.openPositions} vị trí đang tuyển`}
                    size="small"
                    sx={{
                        backgroundColor: theme.palette.primary.light,
                        color: theme.palette.primary.main,
                        fontWeight: 500,
                        fontSize: '0.75rem',
                        height: 28,
                        '& .MuiChip-label': {
                            px: 1.5,
                            color: theme.palette.primary.main
                        },
                        '& .MuiChip-icon': {
                            color: theme.palette.primary.main
                        }
                    }}
                />

                {/* Company Rating/Verification */}
                {company.isVerified && (
                    <Chip
                        label="Đã xác thực"
                        size="small"
                        sx={{
                            backgroundColor: theme.palette.success.light,
                            color: theme.palette.success.main,
                            fontWeight: 500,
                            fontSize: '0.75rem',
                            height: 28,
                            '& .MuiChip-label': {
                                px: 1.5,
                                color: theme.palette.success.main
                            }
                        }}
                    />
                )}
            </Box>

            {/* Follow Button - Bottom Right Corner */}
            {showFollow && (
                <Box position="absolute" bottom={12} right={12} zIndex={1}>
                    <IconButton
                        size="small"
                        sx={{
                            color: company.isFollowed ? theme.palette.error.main : theme.palette.grey[500],
                            backgroundColor: 'transparent',
                            padding: '6px',
                            borderRadius: '50%',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: company.isFollowed 
                                    ? theme.palette.error.light 
                                    : theme.palette.grey[200],
                                color: company.isFollowed 
                                    ? theme.palette.error.main 
                                    : theme.palette.text.primary
                            }
                        }}
                        onClick={handleFollowClick}
                        aria-label="Theo dõi công ty"
                    >
                        {company.isFollowed ? <FollowFilledIcon fontSize="small" /> : <FollowIcon fontSize="small" />}
                    </IconButton>
                </Box>
            )}
        </Box>
    );

    return (
        <MainCard
            content={false}
            sx={{
                ...getCardSx(),
                ...sx
            }}
            onClick={handleCompanyClick}
        >
            {companyContent}
        </MainCard>
    );
};

export default CompanyItem;
