import React from 'react';
import { Box, Grid, Button, Stack } from '@mui/material';

// project imports
import CompanyItem from './CompanyItem';
import CompanyItemSkeleton from './CompanyItemSkeleton';
import { ICompanyListProps } from 'types/company';

const CompanyList: React.FC<ICompanyListProps> = ({
    companies,
    loading = false,
    variant = 'grid',
    itemVariant = 'default',
    showLoadMore = false,
    showFilters = false,
    onLoadMore,
    onCompanyClick,
    onFollowClick,
    onFiltersChange
}) => {
    if (loading && companies.length === 0) {
        return (
            <Box>
                {variant === 'grid' ? (
                    <Grid container spacing={3}>
                        {Array.from({ length: 6 }).map((_, index) => (
                            <Grid item xs={12} md={6} key={index}>
                                <CompanyItemSkeleton variant={itemVariant} />
                            </Grid>
                        ))}
                    </Grid>
                ) : (
                    <Stack spacing={2}>
                        {Array.from({ length: 8 }).map((_, index) => (
                            <CompanyItemSkeleton key={index} variant={itemVariant} />
                        ))}
                    </Stack>
                )}
            </Box>
        );
    }

    if (!loading && companies.length === 0) {
        return (
            <Box
                textAlign="center"
                py={8}
                sx={{
                    color: 'text.secondary'
                }}
            >
                <Box mb={2}>
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10zm-2-8h-2v2h2v-2zm0 4h-2v2h2v-2z"/>
                    </svg>
                </Box>
                Không tìm thấy công ty nào phù hợp
            </Box>
        );
    }

    const renderCompanyItems = () => {
        if (variant === 'grid') {
            return (
                <Grid container spacing={3}>
                    {companies.map((company) => (
                        <Grid item xs={12} md={6} key={company.id}>
                            <CompanyItem
                                company={company}
                                variant={itemVariant}
                                onCompanyClick={onCompanyClick}
                                onFollowClick={onFollowClick}
                            />
                        </Grid>
                    ))}
                </Grid>
            );
        }

        // List variant
        return (
            <Stack spacing={2}>
                {companies.map((company) => (
                    <CompanyItem
                        key={company.id}
                        company={company}
                        variant={itemVariant === 'default' ? 'list' : itemVariant}
                        onCompanyClick={onCompanyClick}
                        onFollowClick={onFollowClick}
                    />
                ))}
            </Stack>
        );
    };

    return (
        <Box>
            {/* Company Items */}
            {renderCompanyItems()}

            {/* Loading more items */}
            {loading && companies.length > 0 && (
                <Box mt={3}>
                    {variant === 'grid' ? (
                        <Grid container spacing={3}>
                            {Array.from({ length: 2 }).map((_, index) => (
                                <Grid item xs={12} md={6} key={`loading-${index}`}>
                                    <CompanyItemSkeleton variant={itemVariant} />
                                </Grid>
                            ))}
                        </Grid>
                    ) : (
                        <Stack spacing={2}>
                            {Array.from({ length: 3 }).map((_, index) => (
                                <CompanyItemSkeleton key={`loading-${index}`} variant={itemVariant} />
                            ))}
                        </Stack>
                    )}
                </Box>
            )}

            {/* Load More Button */}
            {showLoadMore && onLoadMore && !loading && (
                <Box textAlign="center" mt={4}>
                    <Button
                        variant="outlined"
                        size="large"
                        onClick={onLoadMore}
                        sx={{
                            px: 4,
                            py: 1.5,
                            borderRadius: 2,
                            fontSize: '1rem',
                            fontWeight: 600
                        }}
                    >
                        Xem thêm công ty
                    </Button>
                </Box>
            )}
        </Box>
    );
};

export default CompanyList;
