{"0": "Level 0", "1": "Level 1", "2": "Level 2", "3": "Level 3", "4": "Level 4", "5": "Level 5", "=======================COMMON=======================": "Common", "select-all": "-- All ---", "select-option": "-- Select ---", "show-filters": "Show filters", "hide-filters": "Hide filters", "warning": "Warning", "value": "Value", "key": "Key", "change-password-successfully": "Change password successfully!", "ok": "OK", "=======================MENU LIST=======================": "Menu-list", "administration": "Administration", "reports": "Reports", "show-more": "Show more", "summary": "Summary", "no-data": "No data", "download-word": "Download word", "change-password": "Change password", "current-password": "Current password", "new-password": "New password", "confirm-password": "Confirm password", "=======================ERROR=======================": "ERROR", "user-already-exist": "User already exist", "required-avatar": "Avatar is required", "update-effort-plan-error": "Update effort plan error", "something-is-wrong": "Something is wrong", "error-not-found-screen-centent": "The page you are looking was moved, removed, renamed, or might never exist!", "error-expired-account-screen-centent": "Your account has expired to use this service package. Please contact VSII to limit continued use of the application", "email-sent": "Password reset notification sent to Email. Please access Email to check", "=======================VALIDATE_MESSAGES=======================": "validate message", "required": "This field is required", "invalid-number": "This field must be a numeric value", "positive-number": "This field must be a positive number", "less-or-equal": "This field must be less than or equal to", "larger-or-equal": "This field must be larger than or equal to", "less-than": "This field must be less than", "larger-than": "This field must be larger than", "trim": "This field must be a trimmed string", "one-decimal": "Must have exactly one decimal place after the decimal point", "outboarddate": "The outboard date must not be less than the onboard date", "enddate": "The end date must not be less than the start date", "after-day": "To date must be after from date", "date-format": "The date format is not correct", "special-characters": "Do not enter special characters", "number": "Please enter a valid number.", "max-length": "Please enter less than 5000 characters", "future-dates": "Cannot choose a date in the future", "about-days": "Date added must be after the existing date", "special-characters-parter-name": "Partner name does not contain special characters", "special-characters-supplier-name": "Supplier name does not contain special characters", "no-number": "Do not enter numeric characters", "date-of-existence": "New add date already exists", "invalid-name-file": "File format must be .xlsx", "invalid-password-min": "Password must be at least 8 characters", "invalid-password-max": "Password must be at most 24 characters", "invalid-password-special": "Password contains at least one uppercase letter, one lowercase letter, one number and one special character", "invalid-citizenId-max": "Citizen ID must be at least 12 characters", "invalid-businessId-max": "Business License Number must be at least 10 characters", "invalid-taxCode-max": "Tax Code must be at least 13 characters", "invalid-code-name": "This field must contain only English letters (a-z, A-Z), numbers, and special characters (up to 1)", "repassword-not-matching": "Confirm password not match new password", "=======================ADMINISTRATION=======================": "Administration", "manage-user": "Manage user", "manage-project": "Manage project", "manage-group": "Manage group", "manage-department": "Manage department", "project-type-config": "Project type config", "title-config": "Title config", "non-billables-config": "Non-billables Config", "flexible-reporting": "Flexible Reporting", "column-config": "Column Config", "text-config": "Language Config", "flexible-reporting-config": "Flexible Reporting Configuration", "=======================DASHBOARD=======================": "Dashboard", "dashboard": "Dashboard", "welcome": "Welcome back,", "system-title": "VSII REPORTING SYSTEM", "accessibility": "Accessibility: ", "accessibility-content": "Online reporting systems can be accessed from anywhere with an internet connection, making it easy for users to submit and view reports at any time.", "efficiency": "Efficiency: ", "efficiency-content": "Online reporting systems can streamline the reporting process by automating certain tasks, reducing the need for manual data entry and minimizing the risk of errors.", "real-time": "Real-time updates: ", "real-time-content": "Online reporting systems can provide real-time updates on the status of reports, allowing users to track their progress and make informed decisions.", "=======================PERMISSION=======================": "Permission", "all": "All", "report": "Report", "rp-wer": "Weekly Effort report", "rp-wer-member-view": "View report - Member", "rp-wer-project-view": "View report - Project", "rp-wer-download": "Download report", "rp-wer-project-detail-view": "View report - Project Detail", "rp-wer-project-detail-pm-verify": "PM <PERSON><PERSON><PERSON>", "rp-wer-project-detail-qa-verify": "QA Verify", "rp-wer-member-comment": "Comment", "rp-mer": "Monthly Effort report", "rp-mer-sum-view": "View report - Summary", "rp-mer-sum-edit": "Edit the status of Effort Plan - Summary", "rp-mer-project-view": "View report - Project", "rp-mer-member-view": "View report - Member", "rp-mer-member-members-view": "View report - Member - Tab Member", "rp-mer-member-projects-view": "View report - Member - Tab Projects", "rp-mer-download": "Download report", "rp-mer-project-download": "Download project report", "rp-mer-sum-download": "Download summary report", "rp-mer-member-download": "Download member report", "rp-mer-project-comment": "Comment summary", "rp-mer-project-report-view": "View project report", "rp-mer-project-report-add": "Add Project Report", "rp-mer-project-report-edit": "Edit project report", "rp-mer-project-report-delete": "Delete project report", "rp-mer-project-report-download": "Dowload Project Report", "rp-nonbill": "Non-billable report", "rp-nonbill-member-view": "View report - Member", "rp-nonbill-chart-view": "View report - Cost By Week", "rp-nonbill-warning-member-view": "View report - Warning Nonbillable Member", "rp-nonbill-download": "Download report", "rp-nonbill-comment": "Comments", "rp-nonbill-comment-detail": "Comment by member", "rp-rip": "Resources In Projects", "rp-rip-listhc-view": "View report", "rp-rip-download": "Download report", "rp-rip-listhc-comment": "Comment", "rp-rip-listhc-comment-detail": "Comment by member", "rp-wcr": "Weekly Cost Monitoring report", "rp-wcr-mc-view": "View report - Monthly Cost", "rp-wcr-efw-view": "View report - Effort By Week", "rp-wcr-download": "Download report", "rp-wcr-mc-comment": "Comment", "rp-mpc": "Monthly Project Cost", "rp-mpc-sum-view": "View report - Summary", "rp-mpc-download": "Download report", "rp-mpc-dbm-view": "View report - Detail By Month", "rp-mpc-mcd-view": "View report - Monthly Cost Data", "rp-mpc-mcd-add": "Add data report - Monthly Cost Data", "rp-mpc-mcd-edit": "Edit data report - Monthly Cost Data", "rp-mpc-mcd-download-template": "Download template report - Monthly Cost Data", "rp-mpc-mcd-import-template": "Import template report - Monthly Cost Data", "admin": "Administration", "adm-user": "Manage user", "adm-user-view": "View", "adm-user-add": "Add", "adm-user-edit": "Edit", "adm-project": "Manage project", "adm-project-view": "View", "adm-project-add": "Add", "adm-project-edit": "Edit", "adm-hol": "Manage holiday", "adm-hol-view": "View", "adm-hol-add": "Add", "adm-hol-edit": "Edit", "adm-special-hours": "Manage special hours", "adm-special-hours-view": "View", "adm-special-hours-add": "Add", "adm-special-hours-edit": "Edit", "adm-sys-config": "System config", "adm-sys-config-view": "View", "adm-sys-config-edit": "Edit", "adm-group": "Manage group", "adm-group-view": "View", "adm-group-add": "Add", "adm-group-edit": "Edit", "adm-rank": "Manage rank", "adm-rank-view": "View", "adm-rank-edit": "Edit", "adm-email-config": "<PERSON><PERSON>", "adm-email-config-view": "View", "adm-email-config-add": "Add", "adm-email-config-edit": "Edit", "adm-fr": "Flexible Reporting", "adm-fr-column-config-view": "Column Config View", "adm-fr-column-config-edit": "Column Config Edit", "adm-fr-text-config-view": "Language Config View", "adm-fr-text-config-edit": "Language Config Edit", "adm-department": "Manage department", "adm-department-view": "View", "adm-department-add": "Add", "adm-department-edit": "Edit", "adm-project-type-config": "Project type config", "adm-project-type-config-view": "View", "adm-project-type-config-add": "Add", "adm-project-type-config-edit": "Edit", "adm-title-config": "Title config", "adm-title-config-view": "View", "adm-title-config-add": "Add", "adm-title-config-edit": "Edit", "sale": "Sale", "sale-monthly-production-performance": "Monthly Production Performance", "sale-monthly-production-performance-view": "View", "sale-monthly-production-performance-edit": "Edit", "sale-monthly-production-performance-add": "Add", "sale-monthly-production-performance-delete": "Delete", "sale-monthly-production-performance-download": "Download", "sale-sale-list-request": "Sale List Request", "sale-sale-list-request-view": "View", "sale-sale-list-request-add": "Add", "sale-sale-list-request-edit": "Edit", "sale-sale-list-request-delete": "Delete", "sale-sale-list-request-download": "Download", "sale-sale-list-supplier": "Sale List Supplier", "sale-sale-list-supplier-view": "View", "sale-sale-list-supplier-add": "Add", "sale-sale-list-supplier-edit": "Edit", "sale-sale-list-supplier-delete": "Delete", "sale-sale-list-supplier-download": "Download", "syn": "Synchronize", "synchronize": "Synchronize", "synchronize-data": "Synchronize", "confirm-synchronize": "Are you sure to sync data?", "confirm-register": "Are you sure to register?", "sync-successfully-message": "Sync data successfully", "data-source": "Data Source", "attachment": "Attachment", "user": "User", "sale-salepipeline-summary": "Summary", "sale-salepipeline-summary-view": "View", "sale-salepipeline-summary-download": "Download", "sale-salepipeline-ongoing": "On-Going", "sale-salepipeline-ongoing-view": "View", "sale-salepipeline-ongoing-add": "Add", "sale-salepipeline-ongoing-edit": "Edit", "sale-salepipeline-ongoing-delete": "Delete", "sale-salepipeline-budgeting": "Budgeting Plan", "sale-salepipeline-budgeting-view": "View", "sale-salepipeline-budgeting-edit": "Edit", "sale-salepipeline-bidding": "All sales pipeline", "sale-salepipeline-bidding-view": "View", "sale-salepipeline-bidding-add": "Add", "sale-salepipeline-bidding-edit": "Edit", "sale-salepipeline-bidding-delete": "Delete", "skill-update": "Skills update", "skill-manage-skill-update-view": "View", "skill-manage-skill-update-add": "Add", "skill-manage-skill-update-edit": "Edit", "skill-manage-skill-update-download": "Download", "skill-manage-skill-update-viewcv": "View CV", "skill-report": "Skills report", "skill-manage-skill-report-view": "View", "skill-manage-skill-report-download": "Download", "error-month-update": "The month is not within the project's from date and to date!", "adm-cvconfig": "CV config", "adm-cvconfig-technology-view": "View Technology", "adm-cvconfig-technology-add": "Add Technology", "adm-cvconfig-technology-edit": "Edit Technology", "adm-cvconfig-language-view": "View Language", "adm-cvconfig-language-add": "Add Language", "adm-cvconfig-language-edit": "Edit Language", "adm-cvconfig-reference-view": "View Reference", "adm-cvconfig-reference-add": "Add Reference", "adm-cvconfig-reference-edit": "Edit Reference", "adm-exchangerate": "Exchange Rate Config", "adm-exchangerate-add": "Add exchange rate config", "adm-exchangerate-view": "View exchange rate config", "adm-exchangerate-edit": "Edit exchange rate config", "hr-ot": "Manage ot requests", "hr-ot-view": "View", "hr-ot-add": "Add", "hr-ot-edit": "Edit", "hr-ot-approve": "Approve", "hr-ot-delete": "Delete", "hr-leaves": "Manage leaves", "hr-leaves-view": "View", "hr-leaves-add": "Add", "hr-leaves-edit": "Edit", "hr-leaves-approve": "Approve", "hr-leaves-delete": "Delete", "hr-resignation": "Manage resignation", "hr-resignation-view": "View", "hr-resignation-add": "Add", "hr-resignation-edit": "Edit", "hr-resignation-approve": "Approve", "sale-project-reference": "Project Reference", "sale-project-reference-view": "View", "sale-project-reference-download": "Download", "phone-number-max": "The phone number must not exceed 10 numbers", "phone-number-format": "The phone number is not in the correct format", "rp-product": "Products report", "rp-product-view": "View products report", "required-upgrade-version": "Upgrade to unlock this feature", "upgrade-btn": "Upgrade Now", "rp-mer-orm-report-delete": "Delete ORM report", "rp-mer-orm-report-add": "Add ORM report", "rp-mer-orm-report-export": "Export ORM report", "rp-mer-orm-report-view": "View ORM report", "rp-gr-project-report-view": "Project Report View", "rp-gr-orm-report-view": "ORM report view", "rp-gr-product-report-view": "Product report view", "rp-gr-orm-report-delete": "Delete ORM report", "rp-gr-orm-report-add": "Add ORM report", "rp-gr-orm-report-export": "Export ORM report", "rp-gr-project-report-add": "Add project report", "rp-gr-project-report-edit": "Edit project report", "rp-gr-project-report-download": "Download project report", "rp-gr": "General report", "adm-non-billables-config": "Non-billables Config", "adm-non-billables-config-view": "View", "adm-non-billables-config-edit": "Edit", "adm-fr-flexible-reporting-config-delete": "Flexible Reporting Config - Delete", "adm-fr-flexible-reporting-config-view": "Flexible Reporting Config - View", "adm-fr-flexible-reporting-config-edit": "Flexible Reporting Config - Edit", "adm-fr-flexible-reporting-config-add": "Flexible Reporting Config - Add", "sale-salepipeline-summary-edit-rows": "Edit Arrangement", "sale-salepipeline-ongoing-edit-rows": "Edit Arrangement", "sale-salepipeline-bidding-edit-rows": "Edit Arrangement", "sale-salepipeline-budgeting-edit-rows": "Edit Arrangement", "sale-monthly-production-performance-edit-rows": "Edit Arrangement", "adm-fr-text-config-delete": "Delete Language", "adm-fr-text-config-add": "Add Language", "=======================MANAGE GROUP=======================": "Manage group", "group-code": "Group code", "group-name": "Group name", "group-type": "Group type", "group-info": "Group info", "permission": "Permission", "add-group": "Add group", "edit-group": "Edit group", "assigned-user": "Assigned user", "//3": "Reports", "weekly-effort": "Weekly effort", "monthly-effort": "Monthly effort", "non-billable-monitoring": "Non-billables Monitoring", "list-project-team": "Resources In Projects", "cost-effort-monitoring": "Cost & Effort Monitoring", "monthly-project-cost": "Monthly Project Cost", "//4": "Monthly effort", "monthly-effort-summary": "Summary", "monthly-effort-project": "Effort by project", "monthly-effort-department-member": "Effort by member", "project-report": "Project report", "//5": "Monthly Project Cost", "monthly-project-cost-summary": "Summary", "detail-report-cost-by-month": "Detail report by month", "monthly-cost-data": "Monthly Cost", "fix-cost": "Fixed cost", "others": "Others", "//6": "Search", "search": "Search", "member-code": "Member Code", "user-name": "Username", "password": "Password", "department": "Dept.", "department-full": "Department", "status": "Status", "project": "Projects", "project-type": "Project Type", "year": "Year", "weeks": "Weeks", "timmesheet-status": "Timmesheet status", "member": "Members", "month": "Month", "action": "Actions", "add": "Add new", "undo": "UNDO", "partner-name": "Partner Name", "supplier-name": "Supplier Name", "//7": "Table", "no": "No", "first-name": "First Name", "last-name": "Last Name", "full-name": "Full name", "table-title": "Title", "level": "Level", "project-name": "Project Name", "billable": "Billable", "start-date": "Start Date", "end-date": "End date", "effort-in-week": "Effort in week (hours)", "effort-in-month": "Effort in month (hours)", "difference-hours": "Difference hours", "total-effort": "Total efforts (Manhours)", "total-effort-md": "Total effort (Manday)", "not-logtime-yet": "Not enough Timesheets ratio", "billable-project": "Billables Projects", "main-headcount": "Main Headcount", "secondary-headcount": "Secondary Headcount", "project-nonbillable": "Non-billable Project", "no-logtime": "No logtime", "overhead-allocated-amt": "Overhead Allocated Amt", "salary-cost": "Salary Cost", "total-cost": "Total Cost (VND)", "created": "Created", "creator": "Creator", "last-update": "Last Update", "user-update": "User Update", "id": "ID", "headcount": "Headcount", "from-date": "From date", "to-date": "To date", "cancel": "Cancel", "submit": "Submit", "time-status": "Timesheet status", "filters-report": "Filters", "joining-project": "% Joining Project", "note": "Note", "type": "Type", "location": "Title", "department-short-name": "Department", "department-name": "Department Name", "project-type-short-name": "Project Type", "project-type-name": "Project Type Name", "project-type-create-billable": "Billable", "project-type-create-short-name": "Project Type", "project-type-create-name": "Project Type Name", "project-type-create-color": "Color", "title-short-name": "Title", "title-name": "Title Name", "//8": "Monthly effort summary", "number-of-projects": "Projects Statistics of Departments", "development-projects": "Development projects", "maintenance-projects": "Maintenance projects ", "outsourcing-projects": "Outsourcing projects", "presale-projects": "Presale projects", "product-projects": "Product projects", "training-projects": "Training projects", "other-joint-projects": "Other project", "leave": "Leaves", "update-effort-plan": "Update effort plan", "effort-plan": "Effort plan", "send-report": "Number of projects sent report in month ", "no-send-report": "Number of projects haven't sent report in month ", "number-of-projects-submitted": "Number of projects sent monthly report", "unsubmitted project number": "Number of projects that have not sent monthly reports", "sent-monthly": "Sent monthly report", "not-sent-monthly": "Have not sent monthly reports", "creation-and-log-time": "Redmine logtime statistics", "standard-time": "Number of people claiming to meet standard hours", "less-standard-time": "Number of members log < Standard hours", "more-standard-time": "Number of members log > Standard hours", "effort-allocation": "Effort distribution statistics by project type", "effort-md": "<PERSON><PERSON><PERSON> (md)", "setting-project-type-by-department": "Displays the number of departmental projects", "select-project-type-by-department": "Departments want visibility", "show-chart": "Show", "//9": "Monthly effort project", "work-completed": "% Work Completed", "total-quota": "Total Quota", "previous-effort": "Previous Effort", "total-used-effort": "Total Used Effort", "remaining-cost": "Remaining Cost", "remaining-effort": "<PERSON><PERSON><PERSON>", "add-project": "Add Project", "//10": "Monthly effort project", "contract-no": "Contract No", "contract-size": "Contract Size", "license": "License", "expense": "Costs Through", "remaining-value": "Budget ", "remaining-accumulation": "Remaining accumulation", "//11": "month", "jan": "Jan", "feb": "Feb", "mar": "Mar", "apr": "Apr", "may": "May", "jun": "Jun", "jul": "Jul", "aug": "Aug", "sep": "Sep", "oct": "Oct", "nov": "Nov", "dec": "Dec", "thirteenth-salary": "13th Salary", "y-t-d": "Y-T-D", "//12": "non-billable-monitoring", "staff-no-logtime": "Not enough timesheets", "staff-join-PRD": "Member in PRD projects", "effort-of-staff-50": "Member's efforts >= 50%, not in PRD projects ", "effort-of-staff-80": "Member's efforts >= 80%, not in PRD projects", "non-bill-able-holiday": "Holiday", "number-of-working-days-week": "Number of working days/week", "total-mandays-week": "Total mandays/week", "total-mandays-presale-week": "Total presale mandays/week", "total-mandays-week-no-presale": "Total mandays/week, not include presale", "mandays-payroll": "Manday on payroll only", "total-cost-week": "Total cost/week", "total-cost-month": "Total cost/month", "percentage-cost": "Non-billables Percentage", "nonbill-ratio-chart": "NBM Ratio Chart", "budget-by-week": "Non-billables Percentage", "non-billable-by-member": "NBM by Member", "warning-nonbillable-member": "Warning NBM Member", "consecutive-week": "NBM Consecutive Week", "total-member-junior": "Total Non-billables Juniors members", "total-member-middle": "Total Non-billable Middles members", "total-member-senior": "Total Non-billable Seniors members", "total-member-team-lead": "Total Non-billable Team Leads members", "effort-ratio-junior": "Non-billables efforts ratio of Juniors members", "effort-ratio-middle": "Non-billables efforts ratio of Middles members", "effort-ratio-senior": "Non-billables efforts ratio of Seniors members", "effort-ratio-team-lead": "Non-billables efforts ratio of Team Leads members", "//13": "Cost Monitoring", "NBM-ratio": "NBM Ratio Chart (%)", "cost-statistics": "Cost statistics (VND)", "cost-limit": "Cost limit", "actual-cost": "Actual cost", "accumulated-cost": "Accumulated Cost", "accumulated-effort": "Accumulated <PERSON><PERSON><PERSON>", "statistic-effort": "Effort statistic (manday)", "cost-chart": "Cost chart in ", "actual-effort": "Actual Effort", "effort-chart": "Effort chart in ", "monthly-monitoring": "Monthly Monitoring", "weekly-monitoring": "Weekly Monitoring", "budget": "Budget", "bid-price": "<PERSON><PERSON>", "cost-vnd": "Cost (VND)", "effort_chart": "<PERSON><PERSON><PERSON>", "bidding-comment": "Comment", "estimated-chart": "Weekly effort and cost correlation chart", "estimated-cost": "Estimated cost", "=======================Message=======================": "message", "update": "Update", "success": "Success", "delete": "Delete", "edit": "Edit", "reset-password": "Reset password", "error-login": "Account or password incorrect", "update-project-success": "Project has been updated", "project-user-update": "Headcount has been update", "project-user-add": "Headcount has been add", "delete-success": "Delete success", "messege-delete": "Do you want to remove ", "delete-record": "Are you sure you want to delete the record?", "delete-repord": "Are you sure you want to delete this report?", "overwrite-record": "The record already exists, are you sure you want to overwrite the record?", "comment-success": "Comment success", "add-failed": "Add failed", "edit-failed": "Edit failed", "record-already-exists": "Record already exists", "add-project-success": "Add project success!", "=======================Add - Edit user=======================": "Add - edit user", "edit-user": "Edit user", "add-user": "Add user", "user-id": "User ID", "onboard-date": "Onboard Date", "outboard-date": "Outboard Date", "user-info": "User info", "group": "Group", "confirm": "Confirm", "download-report": "Download report", "period": "Period", "update-success": "Update success", "add-success": "Add success", "error-file": "Import wrong file format", "add-special-hours-success": "Add Special Hour successfully", "edit-special-hours-success": "Edit Special Hour successfully", "sph-exist": "This date range is existed", "mgh-exist": "This date range is existed", "on-outboard-info": "On/outboard info", "rank-user": "Rank", "title-user": "Title", "project-permission": "Project Permission", "detail-by-project": "Detail by Project", "search-groups": "Type to filter group...", "search-project": "Type to filter project...", "management-level": "Management level", "staff-level": "Staff level", "official-business-day": "Official business day", "fullname": "Fullname", "//16": "filter", "active": "Active", "in-active": "InActive", "closed": "Closed", "archived": "Archived", "enough": "Enough", "not-enough": "Not enough", "exceed": "Exceed", "logtime-ratio-chart": "Logtime Ratio Chart", "title": "Multi Language", "home": "Home", "change": "Change Language", "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiatnulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollitanim id est laborum.", "account-settings": "Account settings", "logout": "Logout", "more-items": "More Items", "manage-special-hours": "Manage special hours", "effort-quota": "<PERSON><PERSON><PERSON>", "edit-project": "Edit project", "project-info": "Project Info", "add-headcount": "Add Headcount", "select-group": "Select group", "project-id": "Project ID", "license-amount": "License Amount", "project-manager": "Project Manager", "edit-actual-cost": "Edit Actual Cost", "add-actual-cost": "Add Actual Cost", "overhead-allocated-amount": "Overhead Allocated Amount", "error-message": "Error message", "accept-only-new": "Accept Only New", "accept-all": "Accept All", "edit-headcount": "Edit headcount", "rows-per-page ": "Rows per page ", "page": "Page", "contractor": "Contractor", "contractor-yes": "Yes", "contractor-no": "No", "select-group ": "Select group ", "select-project": "Select project", "type-filter-group": "Type to filter group...", "//17": "Manage holiday", "manage-holiday": "Manage holidays", "current-year": "Current Year", "annual": "Annual", "time": "Time", "holiday-type": "Holiday type", "holiday": "Holiday Type", "edit-holiday": "Edit holiday", "note_placeholder": "Enter note", "//18": "System config", "system-config": "System config", "add-holiday": "Add holiday", "edit-config": "Edit Config", "//19": "Special Hours", "hours-per-day": "Hours/Day", "special-holiday-type": "Special Hours Type", "edit-special-hours": "Edit Special Hours", "add-special-hours": "Add Special Hours", "post-pregnancy": "Post-pregnancy", "maternity": "Maternity leave ", "unpaid-leave": "Unpaid leave", "other": "Other", "//20": "Project headcount", "project-headcount": "Project Headcount", "//21": "Manage rank", "manage-rank": "Manage rank", "rank-name": "Rank name", "edit-rank": "Edit Rank", "rank-cost": "Rank cost", "=======================Add - Edit department=======================": "Add - edit department", "edit-department": "Edit Department", "add-department": "Add Department", "=======================Add - Edit project type config=======================": "Add - edit project type config", "edit-project-type-config": "Edit Project Type", "add-project-type-config": "Add Project Type", "=======================Add - Edit title config=======================": "Add - edit title config", "edit-title-config": "Edit Title", "add-title-config": "Add Title", "=======================Sales report=======================": "Sales report", "sales-report": "Sales report", "monthly-production-performance": "Monthly production performance", "sales-lead": "Sales lead", "add-productivity": "Add productivity", "edit-productivity": "Edit productivity", "request": "Request", "timeline": "Timeline", "add-request": "Add Request", "edit-request": "Edit Request", "add-supplier": "Add Supplier", "edit-supplier": "Edit Supplier", "delete-sale-opportunity": "Delete Sale Opportunity", "delete-message": "Do you want to delele this record?", "not-start": "Not Start", "inprogress": "Inprogress", "pending": "Pending", "close": "Close", "redmine-name": "Redmine Name", "size": "Size", "service-type": "Service Type", "contract-type": "Contract Type", "stop": "Stop", "pic-user-name": "PIC", "technology": "Technology", "quantity": "Quantity", "possibility": "Possibility", "domain": "Domain", "received-date": "Received Date", "requests-checking": "Requests Checking", "supplier-checking": "Supplier Checking", "unit-price": "Unit Price", "work-type": "Work Type", "high": "High", "normal": "Normal", "low": "Low", "remote": "Remote", "onsite": "Onsite", "number-headcount": "Number Headcount", "exist-project-name": "Newly added project name already exists", "exchange-rate": "Exchange rate", "contract-allocation-by-month": "Contract Allocation by Month", "payment-term": "Payment Term (month)", "standard-working-day": "Standard working day", "total": "Total", "role": "Role", "rate": "Rate", "rate-usd": "Rate ( USD )", "rate-vnd": "Rate ( VND )", "monthly-billable": "Monthly billable", "amount": "Amount", "estimate": "Estimate", "delivered": "Delivered", "receivable": "Receivable", "received": "Received", "financial": "Financial", "sales-pipeline": "Sales pipeline", "on-going": "On-going", "bidding": "Bidding", "error-edit-productivity": "The monthly allocation contract does not match the start and end dates!", "unit": "Unit", "estimate-success": "Estimate success", "//23": "quota", "quota-update-history": "Update quota history", "maintain-quota": "Maintain quota", "update-date": "Update date", "quota": "<PERSON><PERSON><PERSON>", "//24": "comment", "comment": "Comments", "update-report-comment": "Update Report Comment:", "update-comment": "Update comment:", "last-update-comment": "Last update by:", "//25": "Weekly effort Project Detail", "project-detail": "Project detail", "spent-time-detail": "Spent Time Detail", "spent-time": "Spent time", "date": "Date", "task-name": "Task name", "payable-ot": "Payable OT", "non-payable-ot": "Non payable OT", "effort-pm-verified": "Effort PM verified", "payable-ot-verified": "Payable OT verified", "non-payable-ot-verified": "Non payable OT verified", "pm-verifed": "PM verifed", "pm-verified-date": "PM verified date", "qa-verifed": "QA verifed", "qa-verified-date": "QA verified date", "message-verify": "Do you want to verify Project's effort ?", "qa-verify": "QA verify", "pm-verify": "PM verify", "pm-not-verify": "The selected record needs to be verified by PM first", "pm-verified-successfully": "Record Verified Successfully", "pm-verified": "The selected record was already verified", "qa-verified-successfully": "Record Verified Successfully", "pm-and-qa-verified": "The selected record was already verified", "project-details-selection": "Must select record before authentication", "//26": "<PERSON><PERSON>", "email-config": "<PERSON><PERSON> config", "add-email-config": "Add <PERSON><PERSON> Config", "edit-email-config": "Edit Email Config", "email-code": "Email Code", "send-to": "Send to", "send-cc": "Send CC", "send-bcc": "Send BCC", "template": "Template", "time-setting": "Time Setting", "name-file": "Attachment File Name", "subject": "Subject", "content": "Content", "email-type": "Email Type", "weekdays": "Weekdays", "hour": "Hour", "week": "Week", "day-in-month": "Day in month", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "//27": "Sale pineline summary", "indicator": "INDICATOR", "usd": "USD", "vnd": "VND", "revenue-estimation": "REVENUE ESTIMATION (VND)", "1st-quarter": "1st Quarter", "2nd-quarter": "2nd Quarter", "3rd-quarter": "3rd Quarter", "4th-quarter": "4th Quarter", "total-revenue-estimation": "TOTAL REVENUE ESTIMATION (VND)", "//26.1": "CV config", "cv-config": "CV config", "add-skill": "Add skill", "language": "Language", "reference": "Reference", "add-technology-config": "Add technology config", "edit-technology-config": "Edit technology config", "add-language-config": "Add language config", "edit-language-config": "Edit language config", "language_name": "Language name", "display-name": "Display name", "organization": "Organization", "position": "Position", "address": "Address", "skill-cv-exist": "The new added skill already exists", "edit-reference-config": "Edit reference config", "add-reference-config": "Add reference config", "technology-already-exist": "Technology already exist", "language-already-exist": "Language already exist", "reference-already-exist": "Reference already exist", "//26.2": "Exchange rate config", "exchange-rate-config": "Exchange rate config", "edit-exchange-rate-config": "Edit exchange rate config", "add-exchange-rate-config": "Add exchange rate config", "currency-existed": "New extra currency already exists", "=======================On Going=======================": "On Going", "vsiiOngoingDeal": "VSII On-going deals", "ongoingVsiiSI": "VSII SI", "ongoingSIService": "SI Service + Lic", "pipelineSIService": "SI Service + Lic", "ongoingMaintenaceLicense": "Maintenance license", "pipelineMaintenaceLicense": "Maintenance license", "ongoingMaintenaceApp": "Maintenance App", "pipelineMaintenaceApp": "Maintenance App", "ongoingVsiiITO": "VSII ITO", "ongoingPRD": "PRD", "edit-on-going-detail": "Edit On-going", "add-on-going-detail": "Add On-going", "billable-day": "Billable day", "vsiiSalePipelineUpdate": "VSII Sales Pipeline Update", "pipelineVsiiITO": "VSII ITO", "pipelinegPRD": "PRD", "pipelineVsiiSI": "VSII SI", "totalRevenueFromPipeline": "Total Revenue from pipeline", "toDateDeliveredForAR": "To-date delivered for A/R", "toDateRevenueInAccounting": "To-date revenue in accounting", "totalVsiiPRDLicenseSale": "Total VSII PRD license sales", "totalITOSale": "Total ITO sales", "totalLicenseSale": "Total license sales", "totalVsiiServiceSale": "Total VSII services sales", "SIandDomesticITO": "SI and Domestic ITO", "SIandDomesticITO80percent": "SI and Domestic ITO <80%", "PRD": "PRD", "PRD80percent": "PRD <80%", "InternationITO": "International ITO", "totalDoableRevenue": "Total do-able revenue", "gapEstimateDelivered": "Gap between Estimate vs. Delivered Actual", "gapEstimateAccounting": "Gap between Estimate vs. In Accounting", "totalVsiiRevenue": "Total VSII Revenue", "lossAndNew": "Loss and New", "updatedBalances": "Updated balances", "totalHeadcountInternationalITO": "Total headcount International ITO", "totalHeadcountDomesticITO": "Total headcount domestic ITO", "totalHeadcountVsii": "Total headcount (VSII)", "PrdSalePipeline": "PRD Sales pipeline", "allPotentialLowProbability": "All potential with low or 0% probability", "=======================Working calendar=======================": "Working calendar", "working-calendar": "Working calendar", "register-working-calendar": "Register working calendar", "hr": "Working calendar", "hr-working-calendar": "Register working calendar", "hr-working-calendar-view": "View", "hr-working-calendar-edit": "Edit", "hr-working-calendar-export": "Export", "hr-working-calendar-verify": "Verify", "hr-working-calendar-aprove": "Aprove", "hr-working-calendar-closing": "Closing date", "work-at-office": "Work at office", "work-from-home": "Work from home", "full-day-leave": "Full day leave", "half-day-leave": "Half day leave", "sick-leave": "Sick leave", "maternity-leave": "Maternity leave", "holiday-calendar": "Holiday", "wedding-vacation": "Wedding vacation", "compensatory-leave": "Compensatory leave", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "message-working-calendar": "Message", "message-working-calendar-detail": "You signed up for WFH for the week, please provide a reason to continue. An administrator will review this request.", "message-working-calendar-verify": "Reason registered WFH for the week", "need-approve": "Need Approve", "send": "send", "approve": "Approve", "leave-request": "Application for leave", "new": "New", "declines": "Decline", "verified-success": "Verified success", "update-status-error": "Update status error", "closing-date": "Working calendar closing date", "edit-closing-date": "Edit working calendar closing date", "verify-working-calendar": "Do you want to verify working calendar of all records?", "status-not-approve": "All records must be approved before verify Or Cannot verify before closing date", "update-closing-date-success": "Update Closing date success!", "error-closing-date": "Update Closing date error", "late": "Late", "early": "Early", "member-name": "Member Name", "total-employees": "Total number of employees", "manage-ot-requests": "Manage OT Requests", "manage-leaves": "Manage leaves", "leaves-type": "Leaves type", "ot-type": "OT Type", "approved": "Approved", "awaiting-approve": "Awaiting approve", "awaiting-approve-direct-manager": "Pending Approval – Line Manager", "awaiting-approve-next-manager": "Pending Approval – Department Head", "awaiting-approve-hr": "Pending Approval – HR", "declined": "Declined", "full-annual-leave": "Full annual leave", "half-annual-leave": "Half annual leave", "wedding-leave": "Wedding leave", "half-sick-leave": "Half sick leave", "full-sick-leave": "Full sick leave", "accrued-leave": "Carried-over Leave", "long-service-leave": "Long Service Leave", "bereavement-leave": "Bereavement leave", "approver": "Approver", "approved-date": "Approved date", "edit-leaves": "Edit leaves", "add-leaves": "Add leaves", "reason": "Reason", "project-requirements": "As per project requirements", "urgent-work-requirements": "As per urgent work requirements", "approve-leave-request": "Approve member leave request ?", "approve-success": "Approve success", "decline-success": "Decline success", "the-allowed-date-cannot-be-exceeded": "The allowed date cannot be exceeded", "=======================SALE PIPELINE=======================": "SALE PIPELINE", "sale-pipeline-type": "Sale Pipeline Type", "sale-pipeline-status": "Status", "total-domestic-ito": "Total Domestic ITO", "total-overseas-ito": "Total Overseas ITO", "total-si-mtn-app-mtn-lic": "Total SI, MTN_App, MTN_Lic", "total-overseas-ito-allocated": "Total Overseas ITO (MGT Revenue)", "total-product": "Total Product", "total-license": "Total License Fee", "total-si-allocated": "Total SI, MTN_App, MTN_Lic (MGT Revenue)", "total-domestic-ito-allocated": "Total Domestic ITO (MGT Revenue)", "total-product-allocated": "Total Product (MGT Revenue)", "customer": "Customer", "probability": "Probability", "contract-date": "Contract Date", "contract-due-date": "Contract Due Date", "contract-duration-from": "Contract Duration From", "contract-duration-to": "Contract Duration To", "warranty-time": "Warranty Time", "size-vnd": "Size VND", "size-usd": "Size USD", "acct-receivables": "Acct Receivables", "net-earn": "Net Earn", "management-revenue-allocated": "Management Revenue allocated", "paid": "Paid", "accountant-revenue-allocatedVND": "Accountant revenue allocated (VND)", "remain": "<PERSON><PERSON><PERSON>", "new-sale-usd": "New Sale USD", "quarter-license-1st": " 1st Quarter License", "license-fee": "License Fee", "quarter-license-2nd": "2nd Quarter License", "currency": "<PERSON><PERSON><PERSON><PERSON>", "quarter-license-3rd": "3rd Quarter License", "quarter-license-4th": "4th Quarter License", "billable-hcs": "Billable HCs", "quarter-1st": "1st Quarter New Sale", "hcs": "HCs", "quarter-2nd": "2nd Quarter New Sale", "team-lead-hcs": "TL HCs", "quarter-3rd": "3rd Quarter New Sale", "senior-hcs": "Senior HCs", "quarter-4th": "4th Quarter New Sale", "middle-hcs": "Middle-Senior HCs", "total-new-sale": "Total new sale", "junior-hcs": "Junior HCs", "total-billable": "Total Billable Hours", "contact": "VSII Contact", "phone-number": "Phone", "presale-folder": "Presale folder", "email-address": "Email", "customer-contact": "Customer contact person", "financial-info": "Financial info", "detailed-allocation": "Detail allocation", "other-info": "Other info", "scs-service-prd-sales": "SCS service and PRD sales", "international-ito": "International ITO", "done": "Done", "npl": "NPL", "add-project-detail": "Add project detail", "edit-project-detail": "Edit project detail", "time-duration": "Time duration", "project-does-not-exist": "Project does not exist", "original-contract-size": "Original contract size", "project-redmine-name": "Project Redmine Name", "total-overseas-ito-acct": "Total Overseas ITO (Acct Revenue)", "total-domestic-ito-acct": "Total Domestic ITO (Acct Revenue)", "total-si-mtn-acct": "Total SI, MTN_App, MTN_Lic (Acct Revenue)", "total-product-acct": "Total Product (Acct Revenue)", "contract-size-usd": "Contract size (USD)", "revenue": "%Revenue", "=======================Budgeting Plan=======================": "Budgeting Plan", "total-contract-value": "Total Contract Value (VND)", "total-cost-limit": "Total Cost Limit (VND)", "total-effort-limit": "Total Effort Limit (manhours)", "total-projectSet-revenue": "Total Project Set Revenue (VND)", "total-actual-cost-by-ACD": "Total Actual Cost by ACD (VND)", "budgeting-plan": "Budgeting Plan", "of-months": "#of months", "contracted-value": "Contracted Value VND", "effort-limit-man-hours": "Effort <PERSON>it manhours", "cost-limit-VND": "Cost Limit VND", "project-set-revenue-VND": "Project Set Revenue VND", "estimated-KPI-project-saving-cost": "Estimated KPI Project Saving Cost", "estimated-share-company-profit": "Estimated Share company profit", "estimated-total-KPI-bonus": "Estimated Total KPI bonus", "ito": "ITO", "o-ito": "O.ITO", "si": "SI", "mtn-app": "MTN.App", "mtn-lic": "MTN.Lic", "product": "Product", "edit-budgeting-plan": "Edit Budgeting Plan", "risk-factor": "Risk factor (%)", "cost-limit-vnd": "Cost Limit VND", "project-KPI-score": "Project KPI Score", "project-KPI-bonus": "Project KPI Bonus", "estimated-used-effort": "Estimated Used Effort (man hours)", "effort-KPI-score": "<PERSON><PERSON>ort <PERSON> score", "estimated-use-cost": "Estimated Use Cost (VND)", "cost-KPI": "Cost KPI", "plan-delivery": "Plan Delivery", "deadline-KPI": "Deadline KPI", "total-on-time-delivery": "Total on-time delivery", "task-mgt": "Task Mgt", "KPI-score": "KPI score", "added-ebitda": "Added Ebitda", "project-set-revenue": "Project Set Revenue (VND)", "actual-cost-by-ACD": "Actual Cost by ACD (VND)", "company-rev-actual-cost": "Company Rev / Actual Cost (%)", "KPI-bonus": "KPI Bonus", "project-mgt-performance-level": "Project Mgt Performance Level", "total-KPI-bonus": "Total KPI Bonus", "project-saving-cost": "Project Saving Cost", "risk-factor-validate-min": "Risk factor cannot be less than 0", "risk-factor-validate-max": "Risk factor cannot be greater than 100", "plan-delivery-min": "Plan Delivery cannot be less than 0", "plan-delivery-max": "Plan Delivery cannot be greater than 10", "total-on-time-delivery-min": "Total On-time Delivery cannot be less than 0", "total-on-time-delivery-max": "Total On-time Delivery cannot be greater than 10", "total-on-time-delivery-more": "Total On-time Delivery cannot be greater than Plan Delivery", "task-mgt-min": "Task Mgt cannot be less than 1", "task-mgt-max": "Task Mgt cannot be greater than 10", "invalid-phone-number": "Invalid phone number", "invalid-email": "Invalid email address (Eg: <EMAIL>)", "confirm-record": "Are you sure to update this record?", "appraisal": "Appraisal", "//28": "Bidding", "contract": "Contract", "loss": "Loss", "failed": "Failed", "add-bidding": "Add Bidding", "edit-bidding": "<PERSON>", "total-smm": "Total SI, MTN_App, MTN_Lic", "si-bidding": "Total SI, MTN_App, MTN_Lic (< 80%)", "domestic-ito": "Total Domestic ITO (< 80%)", "prd": "Total Product (<80%)", "total-smm-management": " Total SI, MTN_App, MTN_Lic (MGT Revenue)", "total-domestic-ito-management": "Total Domestic ITO (MGT Revenue)", "total-prd-value-management": "Total Product (MGT Revenue)", "domestic-ito-accountant": "Total Domestic ITO (Acct Revenue)", "sivalue-accountant": "Total SI, MTN_App, MTN_Lic (Acct Revenue)", "prd-accountant": "Total Product (Acct Revenue)", "bidding-confirm-edit-contract": "Do you want to update the project status to Contract ?", "edit-contract-type-service-type": "Contract Type = 'Fixed Cost' will not be selected Service Type = 'Product'", "redmine-project-exist": "Redmine project already exists", "total-overseas-ITO": "Total Overseas ITO", "total-overseas-ITO-MGT": "Total Overseas ITO (MGT Revenue)", "total-overseas-ITO-ACCT": "Total Overseas ITO (Acct Revenue)", "total-overseas": "Total Overseas ITO (< 80%) ", "=======================SKILL MANAGE=======================": "SKILL MANAGE", "manage-skills": "Manage Skills", "skills-update": "Skills Update", "skills-report": "Skills Report", "job-title": "Title", "skill": "Skill", "skill-name": "Skill Name", "skill-experience": "Skill Experience", "skills": "Skills", "title-code": "Title", "degree": "Degree", "skill-level": "Skill Level", "diploma-degree": "Diploma Degree", "college-degree": "College Degree", "post-graduated": "Post Graduated", "add-cv": "Add CV", "download": "Download", "pdf": "View PDF", "download-template": "Download Template", "download-cv": "Download CV", "view-cv": "View CV", "company-template": "Company template", "smd-template": "SMD template", "=======================MANAGE LEAVE DAY=======================": "MANAGE LEAVE DAYS", "manage-leave-days": "Manage leave days", "hr-leave-day": "Manage Leave Days", "hr-leave-day-view": "View", "hr-leave-day-edit": "Edit", "total-leave-days": "Total Leave Days", "number-leave-days-used": "Number of leave days used", "remaining-leave-days": "Remaining leave days", "update-leave-days": "Update Leave Days", "edit-leave-days-success": "Edit Leave Days Success", "total-leave-days-last-year": "Total leave days last year", "total-leave-days-this-year": "Total leave days this year", "number-integer": "The input value must be a positive integer", "validate-number-leave-days-used": "Number of leave days used taken must be less than or equal to the total leave days last year and total leave days this year.", "=======================MANAGE RESIGNATION=======================": "MANAGE RESIGNATION", "manage-resignation": "Manage resignation", "edit-resignation": "Edit resignation", "add-resignation": "Add resignation", "approve-resign-request": "Approve member resign request ?", "=======================PROJECT REFERENCE=======================": "PROJECT REFERENCE", "project-reference": "Project reference", "client": "Client", "project-description": "Project Description", "effort": "<PERSON><PERSON><PERSON>", "=======================PRODUCT REPORT=======================": "PRODUCT REPORT", "product-report": "Product Report", "product-report.search.project": "Project", "product-report.table.no": "No", "product-report.table.project": "Project", "product-report.table.sprint": "Sprint", "product-report.table.completed": "% Completed", "product-report.table.totalEfforts": "Total Efforts (hours)", "product-report.table.startDate": "Start Date", "product-report.table.endDate": "End Date", "product-report.table.sprintCost": "Current Sprint Cost", "product-report.table.projectCost": "Total Project Cost", "product-report.modal.title": "Details Project", "product-report.modal.leftSide.title": "Project Info", "product-report.modal.leftSide.button.viewbacklog": "View Product Backlog", "product-report.modal.leftSide.prjInfo.project": "Project", "product-report.modal.leftSide.prjInfo.sprintCost": "Current Sprint Cost", "product-report.modal.leftSide.prjInfo.sprint": "Sprint", "product-report.modal.rightSide.title": "Product Backlog", "product-report.modal.rightSide.table.no": "No", "product-report.modal.rightSide.table.requirement": "Requirements", "product-report.modal.rightSide.table.sprint": "Sprint", "product-report.modal.rightSide.table.status": "Status", "product-report.modal.rightSide.table.totalEffort": "Total Effort", "product-report.modal.rightSide.table.totalProjectEffort": "Total Project Effort:", "product-report.modal.rightSide.backlog.description": "Description", "product-report.modal.rightSide.backlog.table.taskName": "Task Name", "product-report.modal.rightSide.backlog.table.assign": "Assign", "product-report.modal.rightSide.backlog.table.status": "Status", "product-report.modal.leftSide.table.requirement": "Requirement", "product-report.modal.leftSide.table.taskName": "Task Name", "product-report.modal.leftSide.table.assignee": "Assignee", "product-report.modal.leftSide.table.status": "Status", "product-report.modal.leftSide.table.sprintCost": "Current Cost", "product-report.modal.leftSide.table.effort": "<PERSON><PERSON><PERSON>", "product-report.modal.leftSide.chart.title": "% Work Completed", "product-report.modal.leftSide.chart.completed": "Completed:", "product-report.modal.leftSide.chart.notCompleted": "Not Completed:", "product-report.modal.leftSide.chart.totalEffort": "Total Effort:", "product-report.modal.leftSide.chart.tooltipTask": "Task Effort", "product-report.modal.leftSide.chart.tooltipBug": "<PERSON><PERSON>", "=======================Monitor Bidding Packages=======================": "Monitor Bidding Packages", "monitor-bidding-package": "Monitor Bidding Packages", "sale-monitor-bidding-package": "Monitor Bidding Packages", "sale-package-bidding-tracking-view": "View - Package Bidding Tracking", "sale-package-bidding-tracking-download": "Download - Package Bidding Tracking", "sale-package-bidding-report-view": "View - Package Bidding Report", "sale-package-bidding-report-add": "Add - Package Bidding Report", "sale-package-bidding-report-edit": "Edit - Package Bidding Report", "sale-package-bidding-tracking-synchronize": "Synchronize - Package Bidding Tracking", "bidding-tracking": "Bidding Tracking", "bidding-report": "Bidding Report", "bidding-package-name": "Bidding Package Name", "KHLCNT-number": "KHLCNT Number", "date-posting": "Date of posting", "time-bidding-closing": "Time of bidding closing", "form-bidding-participation": "Form of Bidding Participation", "TBMT-number": "TBMT Number", "company": "Company", "keyword": "Keyword", "link": "Link", "bidding-package": "Bidding Package", "information": "Contract awarded", "information-empty": "No information", "add-bidding-packages": "Add bidding packages", "edit-bidding-packages": "Edit bidding packages", "online": "Online", "offline": "Offline", "click-here": "Click here", "require-new-token": "Require new token from muasamcong.mpi.gov.vn", "======================= Onsite Detail Modal =======================": "Monitor Bidding Packages", "onsite-detail.modal.title": "Detail", "onsite-detail.modal.footer.cancel": "Cancel", "onsite-detail.modal.footer.copy": "Copy", "onsite-detail.modal.copied": "<PERSON>pied", "onsite-detail.modal.body.total-member": "Number of OS employees:", "onsite-detail.modal.body.table.no": "No", "onsite-detail.modal.body.table.employee-id": "Employee ID", "onsite-detail.modal.body.table.employee-name": "Employee Name", "onsite-detail.modal.body.table.first-name": "First Name", "onsite-detail.modal.body.table.team": "Team", "onsite-detail.modal.body.table.project-name": "Project Name", "onsite-detail.modal.body.table.onsite-days": "Onsite Days", "=======================project report=======================": " project report ", "project-implementation-phase": "Project Implementation Phase", "project-progress-assessment": "Project Progress Assessment", "add-project-report": "Add Project Report", "edit-project-report": "Edit Project Report", "save": "save", "progress-milestone": "Progress & Milestone", "issue-risk": "Issue & Risk", "resource": "Resource", "sale-up-sales": "Sale & Up-Sales", "next-plan": "Next Plan", "tentative-start-date": "Tentative Start Date", "due-date": "Due Date", "oppotunities/expand": "New oppotunities/expand request", "change-of-sale": "Change of Sale", "potential-revenue": "Potential Revenue", "resource-review": "Resource review", "desciption": "Desciption", "root-cause": "Root Cause", "proposed-solution": "Proposed Solution", "finished-main-tasks-in-month": "Finished Main Tasks in Month", "delayed-not-finished-in-month": "Delayed/ Not finished vs Plan in Month", "completed-milestones": "Completed Milestones", "next-milestone": "Next Milestone", "oppotunities-expand": "New oppotunities/expand request", "======================= ORMreport =======================": "ORMreport", "report-name": "Report Name", "upload-user": "Upload User", "upload": "Upload", "upload-file": "Upload file", "confirm-delete": "Are you sure you want to delete this report?", "total-license-fee": "Total License Fee", "license-fee-budgeting": "License Fee", "orm-report": "ORM Report", "general-report": "General Report", "======================= NonBillables-config =======================": "ORMreport", "config-name": "Name", "======================= Monthly efort Project =======================": "Monthly efort project", "no_effort_data": "Project {projectName} did not generate effort in months {months} so there is no data", "no-effort-incurred": "No effort incurred", "effort-incurred": "Effort incurred", "pm-has-verified-timesheet-qa-has-not-verified": "PM has verified timesheet / QA has not verified timesheet", "pm-has-not-verified-timesheet": "PM has not verified timesheet", "qa-has-verified-timesheet": "QA has verified timesheet", "======================= flexible report config  =======================": "flexible report config", "default-text-en": "Default Text (EN)", "text-en": "New Text (EN)", "new-text-name-vi": "New Text (VI)", "default-text-name-vi": "Default Text (VI)", "sum": "Sum", "layout": "Layout", "condition": "Conditions", "add-config": "Add Config", "======================= Flexible report - Columns config =======================": "Flexible report - Columns config", "calculate": "Calculate", "column-name": "Column name", "input-type": "Input type", "======================= Flexible report - Text config =======================": "Flexible report - Text config", "screen-name": "Screen's Name", "text-name": "Text name", "text-config.default-text-en": "Default Text (EN)", "text-config.default-text-vi": "Default Text (VI)", "text-config.new-text-en": "New Text (EN)", "text-config.new-text": "New Text", "text-config.new-text-vi": "New Text (VI)", "delete-config": "Are you sure you want to delete this config?", "other-data-sources": "Other data sources", "font-weight-bold": "Bold", "background-color": "Background Color", "style": "Style", "reset-style": "Reset Style", "delete-language": "Are you sure you want to delete this language?", "sale-summary-indicator": "INDICATOR", "sale-summary-usd": "USD", "sale-summary-vnd": "VND", "sale-summary-revenue-estimation": "REVENUE ESTIMATION (VND)", "sale-summary-total-revenue-estimation": "TOTAL REVENUE ESTIMATION (VND)", "sale-summary-1st-quarter": "1st Quarter", "sale-summary-2nd-quarter": "2nd Quarter", "sale-summary-3rd-quarter": "3rd Quarter", "sale-summary-4th-quarter": "4th Quarter", "sale-summary-vsiiOngoingDeal": "VSII On-going deals", "sale-summary-ongoingVsiiSI": "VSII SI", "sale-summary-ongoingSIService": "SI Service + Lic", "sale-summary-ongoingMaintenaceLicense": "Maintenance license", "sale-summary-ongoingMaintenaceApp": "Maintenance App", "sale-summary-ongoingVsiiITO": "VSII ITO", "sale-summary-ongoingPRD": "PRD", "sale-summary-vsiiSalePipelineUpdate": "VSII Sales Pipeline Update", "sale-summary-pipelineVsiiSI": "VSII SI", "sale-summary-pipelineSIService": "SI Service + Lic", "sale-summary-pipelineMaintenaceLicense": "Maintenance license", "sale-summary-pipelineMaintenaceApp": "Maintenance App", "sale-summary-pipelineVsiiITO": "VSII ITO", "sale-summary-pipelinegPRD": "PRD", "sale-summary-toDateDeliveredForAR": "To-date delivered for A/R", "sale-summary-toDateRevenueInAccounting": "To-date revenue in accounting", "sale-summary-totalVsiiPRDLicenseSale": "Total VSII PRD license sales", "sale-summary-totalITOSale": "Total ITO sales", "sale-summary-totalLicenseSale": "Total license sales", "sale-summary-totalHeadcountInternationalITO": "Total headcount International ITO", "sale-summary-totalHeadcountDomesticITO": "Total headcount domestic ITO", "sale-summary-summary": "Summary", "budgeting-plan-budgeting-plan": "Budgeting Plan", "budgeting-plan-year": "Year", "budgeting-plan-service-type": "Service Type", "budgeting-plan-type": "Type", "budgeting-plan-project-name": "Project Name", "budgeting-plan-of-months": "#of months", "budgeting-plan-contracted-value": "Contracted Value VND", "budgeting-plan-license-fee-budgeting": "License Fee", "budgeting-plan-effort-limit-man-hours": "Effort <PERSON>it manhours", "budgeting-plan-cost-limit-VND": "Cost Limit VND", "budgeting-plan-project-set-revenue-VND": "Project Set Revenue VND", "budgeting-plan-estimated-KPI-project-saving-cost": "Estimated KPI Project Saving Cost", "budgeting-plan-estimated-share-company-profit": "Estimated Share company profit", "budgeting-plan-estimated-total-KPI-bonus": "Estimated Total KPI bonus", "budgeting-plan-edit-budgeting-plan": "Edit Budgeting Plan", "budgeting-plan-project-info": "Project Info", "budgeting-plan-sale-pipeline-type": "Sale Pipeline Type", "budgeting-plan-appraisal": "Appraisal", "budgeting-plan-cost-limit-vnd": "Cost Limit VND", "budgeting-plan-risk-factor": "Risk factor (%)", "budgeting-plan-note": "Note", "budgeting-plan-project-KPI-score": "Project KPI Score", "budgeting-plan-estimated-used-effort": "Estimated Used Effort (man hours)", "budgeting-plan-estimated-use-cost": "Estimated Use Cost (VND)", "budgeting-plan-plan-delivery": "Plan Delivery", "budgeting-plan-total-on-time-delivery": "Total on-time delivery", "budgeting-plan-effort-KPI-score": "<PERSON><PERSON>ort <PERSON> score", "budgeting-plan-cost-KPI": "Cost KPI", "budgeting-plan-deadline-KPI": "Deadline KPI", "budgeting-plan-task-mgt": "Task Mgt", "budgeting-plan-KPI-score": "KPI score", "budgeting-plan-project-KPI-bonus": "Project KPI Bonus", "budgeting-plan-added-ebitda": "Added Ebitda", "budgeting-plan-project-set-revenue": "Project Set Revenue (VND)", "budgeting-plan-actual-cost-by-ACD": "Actual Cost by ACD (VND)", "budgeting-plan-company-rev-actual-cost": "Company Rev / Actual Cost (%)", "budgeting-plan-project-mgt-performance-level": "Project Mgt Performance Level", "budgeting-plan-project-saving-cost": "Project Saving Cost", "budgeting-plan-KPI-bonus": "KPI Bonus", "budgeting-plan-total-KPI-bonus": "Total KPI Bonus", "budgeting-plan-action": "Actions", "all-sale-pipeline-no": "No", "all-sale-pipeline-contract-type": "Contract Type", "all-sale-pipeline-service-type": "Service Type", "all-sale-pipeline-project-name": "Project Name", "all-sale-pipeline-probability": "Probability", "all-sale-pipeline-status": "Status", "all-sale-pipeline-size-vnd": "Size VND", "all-sale-pipeline-size-usd": "Size USD", "all-sale-pipeline-management-revenue-allocated": "Management Revenue allocated", "all-sale-pipeline-accountant-revenue-allocatedVND": "Accountant revenue allocated (VND)", "all-sale-pipeline-license-fee": "License Fee", "all-sale-pipeline-time-duration": "Time duration", "all-sale-pipeline-customer": "Customer", "all-sale-pipeline-contract-no": "Contract No", "all-sale-pipeline-project-redmine-name": "Project Redmine Name", "all-sale-pipeline-contract-date": "Contract Date", "all-sale-pipeline-contract-duration-from": "Contract Duration From", "all-sale-pipeline-note": "Note", "all-sale-pipeline-contract-duration-to": "Contract Duration To", "all-sale-pipeline-warranty-time": "Warranty Time", "all-sale-pipeline-exchange-rate": "Exchange rate", "all-sale-pipeline-original-contract-size": "Original contract size", "all-sale-pipeline-new-sale-usd": "New Sale USD", "all-sale-pipeline-acct-receivables": "Acct Receivables", "all-sale-pipeline-net-earn": "Net Earn", "all-sale-pipeline-quarter-license-1st": "1st Quarter License", "all-sale-pipeline-paid": "Paid", "all-sale-pipeline-quarter-license-2nd": "2nd Quarter License", "all-sale-pipeline-remain": "<PERSON><PERSON><PERSON>", "all-sale-pipeline-quarter-license-3rd": "3rd Quarter License", "all-sale-pipeline-quarter-license-4th": "4th Quarter License", "all-sale-pipeline-billable-hcs": "Billable HCs", "all-sale-pipeline-quarter-1st": "1st Quarter New Sale", "all-sale-pipeline-hcs": "HCs", "all-sale-pipeline-quarter-2nd": "2nd Quarter New Sale", "all-sale-pipeline-team-lead-hcs": "TL HCs", "all-sale-pipeline-quarter-3rd": "3rd Quarter New Sale", "all-sale-pipeline-senior-hcs": "Senior HCs", "all-sale-pipeline-quarter-4th": "4th Quarter New Sale", "all-sale-pipeline-middle-hcs": "Middle-Senior HCs", "all-sale-pipeline-total-new-sale": "Total new sale", "all-sale-pipeline-junior-hcs": "Junior HCs", "all-sale-pipeline-total-billable": "Total Billable Hours", "all-sale-pipeline-action": "Actions", "all-sale-pipeline-billable-day": "Billable day", "all-sale-pipeline-billable": "Billable", "all-sale-pipeline-add": "Add new", "all-sale-pipeline-contract-allocation-by-month": "Contract Allocation by Month", "all-sale-pipeline-estimate": "Estimate", "all-sale-pipeline-cancel": "Cancel", "all-sale-pipeline-submit": "Submit", "all-sale-pipeline-warning": "Warning", "all-sale-pipeline-messege-delete": "Do you want to remove", "all-sale-pipeline-type": "Type", "all-sale-pipeline-year": "Year", "all-sale-pipeline-search": "Search", "all-sale-pipeline-download-report": "Download report", "all-sale-pipeline-jan": "Jan", "all-sale-pipeline-feb": "Feb", "all-sale-pipeline-mar": "Mar", "all-sale-pipeline-apr": "Apr", "all-sale-pipeline-may": "May", "all-sale-pipeline-jun": "Jun", "all-sale-pipeline-jul": "Jul", "all-sale-pipeline-aug": "Aug", "all-sale-pipeline-sep": "Sep", "all-sale-pipeline-oct": "Oct", "all-sale-pipeline-nov": "Nov", "all-sale-pipeline-dec": "Dec", "all-sale-pipeline-other-info": "Other info", "all-sale-pipeline-contact": "VSII Contact", "all-sale-pipeline-presale-folder": "Presale Folder", "all-sale-pipeline-customer-contact": "Customer Contact Person", "all-sale-pipeline-phone-number": "Phone", "all-sale-pipeline-email-address": "Email", "all-sale-pipeline-add-bidding-packages": "Add bidding packages", "all-sale-pipeline-edit-bidding-packages": "Edit bidding packages", "monthly-production-performance-monthly-production-performance": "Monthly production performance", "monthly-production-performance-project-name": "Project Name", "monthly-production-performance-service-type": "Service Type", "monthly-production-performance-contract-type": "Contract Type", "monthly-production-performance-contract-size-usd": "Contract size (USD)", "monthly-production-performance-delivered": "Delivered", "monthly-production-performance-receivable": "Receivable", "monthly-production-performance-received": "Received", "monthly-production-performance-financial": "Financial", "monthly-production-performance-jan": "Jan", "monthly-production-performance-feb": "Feb", "monthly-production-performance-mar": "Mar", "monthly-production-performance-apr": "Apr", "monthly-production-performance-may": "May", "monthly-production-performance-jun": "Jun", "monthly-production-performance-jul": "Jul", "monthly-production-performance-aug": "Aug", "monthly-production-performance-sep": "Sep", "monthly-production-performance-oct": "Oct", "monthly-production-performance-nov": "Nov", "monthly-production-performance-dec": "Dec", "monthly-production-performance-y-t-d": "Y-T-D", "monthly-production-performance-scs-total": "SCS Total", "monthly-production-performance-scs": "SCS", "monthly-production-performance-prd-smd-total": "PRD & SMD Total", "monthly-production-performance-prd-smd": "PRD & SMD", "monthly-production-performance-comments": "Comments", "monthly-production-performance-company-total": "Company Total", "monthly-production-performance-total-hc": "Total HC", "monthly-production-performance-productivity": "Productivity", "monthly-production-performance-total-product": "Total Product", "monthly-production-performance-total-si": "Total SI", "monthly-production-performance-total-domestic-ito": "Total Domestic ITO", "monthly-production-performance-total-overseas-ito": "Total Overseas ITO", "monthly-production-performance-total-ito": "Total ITO", "monthly-production-performance-total": "Company Total", "monthly-production-performance-edit-headcount": "Edit headcount", "monthly-production-performance-number-headcount": "Number Headcount", "monthly-production-performance-year": "Year", "monthly-production-performance-month": "Month", "monthly-production-performance-department": "Dept.", "monthly-production-performance-original-contract-size": "Original contract size", "monthly-production-performance-currency": "<PERSON><PERSON><PERSON><PERSON>", "monthly-production-performance-from-date": "From date", "monthly-production-performance-to-date": "To date", "monthly-production-performance-exchange-rate": "Exchange rate", "monthly-production-performance-standard-working-day": "Standard working day", "monthly-production-performance-contract-allocation-by-month": "Contract Allocation by Month", "monthly-production-performance-payment-term": "Payment Term (month)", "monthly-production-performance-unit": "Unit", "monthly-production-performance-role": "Role", "monthly-production-performance-rate": "Rate", "monthly-production-performance-rate-usd": "Rate (USD)", "monthly-production-performance-quantity": "Quantity", "monthly-production-performance-amount": "Amount", "monthly-production-performance-estimate": "Estimate", "monthly-production-performance-delivered-usd": "Delivered (USD)", "monthly-production-performance-receivable-usd": "Receivable (USD)", "monthly-production-performance-received-usd": "Received (USD)", "monthly-production-performance-financial-usd": "Financial (USD)", "monthly-production-performance-edit-productivity": "Edit productivity", "all-sale-pipeline-financial-info": "Financial Info", "all-sale-pipeline-project-info": "Project Info", "all-sale-pipeline-detailed-allocation": "Detail Allocation", "all-sale-pipeline-department": "Dept", "all-sale-pipeline-currency": "<PERSON><PERSON><PERSON><PERSON>", "monthly-production-performance-action": "Actions", "on-going-action": "Actions", "on-going-no": "No", "on-going-contract-type": "Contract Type", "on-going-project-name": "Project Name", "on-going-probability": "Probability", "on-going-size-vnd": "Size VND", "on-going-size-usd": "Size USD", "on-going-accountant-revenue-allocatedVND": "Accountant revenue allocated (VND)", "on-going-license-fee": "License Fee", "on-going-time-duration": "Time duration", "on-going-sale-pipeline-type": "Sale Pipeline Type", "on-going-year": "Year", "on-going-project": "Project", "on-going-sale-pipeline-status": "Status", "on-going-search": "Search", "on-going-project-info": "Project Info", "on-going-edit-on-going-detail": "Edit On-going", "on-going-contract-date": "Contract Date", "on-going-contract-duration-from": "Contract Duration From", "on-going-contract-duration-to": "Contract Duration To", "on-going-note": "Note", "on-going-warranty-time": "Warranty Time", "on-going-financial-info": "Financial Info", "on-going-currency": "<PERSON><PERSON><PERSON><PERSON>", "on-going-exchange-rate": "Exchange Rate", "on-going-new-sale-usd": "New Sale USD", "on-going-acct-receivables": "Acct Receivables", "on-going-net-earn": "Net Earn", "on-going-paid": "Paid", "on-going-quarter-license-1st": "1st Quarter License", "on-going-quarter-license-2nd": "2nd Quarter License", "on-going-quarter-license-3rd": "3rd Quarter License", "on-going-quarter-license-4th": "4th Quarter License", "on-going-detailed-allocation": "Detail Allocation", "on-going-billable-hcs": "Billable HCs", "on-going-hcs": "HCs", "on-going-quarter-1st": "1st Quarter New Sale", "on-going-quarter-2nd": "2nd Quarter New Sale", "on-going-quarter-3rd": "3rd Quarter New Sale", "on-going-quarter-4th": "4th Quarter New Sale", "on-going-team-lead-hcs": "TL HCs", "on-going-senior-hcs": "Senior HCs", "Middle-Senior HCs": "Middle-Senior HCs", "on-going-junior-hcs": "Junior HCs", "on-going-total-billable": "Total Billable Hours", "on-going-jan": "HC Jan", "on-going-billable-day": "Billable day", "on-going-other-info": "Other Info", "on-going-contact": "VSII Contact", "on-going-phone-number": "Phone", "on-going-email-address": "Email", "on-going-customer-contact": "Customer Contact Person", "on-going-presale-folder": "Presale Folder", "on-going-remain": "<PERSON><PERSON><PERSON>", "on-going-service-type": "Service Type", "on-going-comment": "Comment", "on-going-on-going-project-info": "Project Info", "on-going-customer": "Customer", "on-going-contract-no": "Contract No", "on-going-management-revenue-allocated": "Management Revenue Allocated", "on-going-scs-service-prd-sales": "SCS service and PRD sales", "on-going-international-ito": "International ITO", "on-going-done": "Done", "on-going-npl": "NPL", "on-going-middle-hcs": "Middle-Senior HCs", "on-going-total-new-sale": "Total New Sale", "on-going-billable": "Billable", "on-going-feb": "HC Feb", "on-going-mar": "HC Mar", "on-going-apr": "HC Apr", "on-going-may": "HC May", "on-going-jun": "HC Jun", "on-going-jul": "HC jul", "on-going-aug": "HC Aug", "on-going-sep": "HC Sep", "on-going-oct": "HC Oct", "on-going-nov": "HC Nov", "on-going-dec": "HC Dec", "sales_lead_requests_sales-lead": "Sales lead", "sales_lead_requests_requests-checking": "Requests Checking", "sales_lead_requests_partner-name": "Partner Name", "sales_lead_requests_received-date": "Received Date", "sales_lead_requests_status": "Status", "sales_lead_requests_pic-user-name": "PIC", "sales_lead_requests_not-start": "Not Start", "sales_lead_requests_inprogress": "Inproress", "sales_lead_requests_stop": "Stop", "sales_lead_requests_request": "Request", "sales_lead_requests_technology": "Technology", "sales_lead_requests_quantity": "Quantity", "sales_lead_requests_timeline": "Timeline", "sales_lead_requests_possibility": "Possibility\t", "sales_lead_requests_domain": "Domain", "sales_lead_requests_note": "Note", "sales_lead_requests_add-request": "Add Request", "sales_lead_requests_high": "High", "sales_lead_requests_normal": "Normal", "sales_lead_requests_low": "Low", "sales_lead_requests_edit-request": "Edit Request", "sales_lead_supplier_supplier-name": "Supplier Name", "sales_lead_supplier_from-date": "From date", "sales_lead_supplier_to-date": "To date", "sales_lead_supplier_pic-user-name": "PIC", "sales_lead_supplier_technology": "Technology", "sales_lead_supplier_quantity": "Quantity", "sales_lead_supplier_unit-price": "Unit Price", "sales_lead_supplier_work-type": "Work Type", "sales_lead_supplier_note": "Note", "sales_lead_supplier_supplier-checking": "Supplier Checking", "sales_lead_supplier_edit-supplier": "Edit Supplier", "sales_lead_supplier_remote": "Remote", "sales_lead_supplier_onsite": "Onsite", "sales_lead_supplier_add-supplier": "Add Supplier", "monitor_bidding_packages_report": "Monitor Bidding Packages", "monitor_bidding_packages_report_bidding-report": "Bidding Report", "monitor_bidding_packages_report_type": "Type", "monitor_bidding_packages_report_bidding-package-name": "Bidding Package Name", "monitor_bidding_packages_report_address": "Address", "monitor_bidding_packages_report_status": "Status", "monitor_bidding_packages_report_KHLCNT-number": "KHLCNT Number", "monitor_bidding_packages_report_budget": "Budget", "monitor_bidding_packages_report_date-posting": "Date of posting", "monitor_bidding_packages_report_time-bidding-closing": "Time of bidding closing", "monitor_bidding_packages_report_bid-price": "<PERSON><PERSON>", "monitor_bidding_packages_report_form-bidding-participation": "Form of Bidding Participation", "monitor_bidding_packages_report_TBMT-number": "TBMT Number", "monitor_bidding_packages_report_group": "Group", "monitor_bidding_packages_report_company": "Company", "monitor_bidding_packages_report_keyword": "Keyword", "monitor_bidding_packages_report_comment": "Comment", "monitor_bidding_packages_report_link": "Link", "monitor_bidding_packages_report_add-bidding-packages": "Add bidding packages", "monitor_bidding_packages_report_edit-bidding-packages": "Edit bidding packages", "monitor_bidding_packages_tracking_bidding-tracking": "Bidding Tracking", "monitor_bidding_packages_tracking_type": "Type", "monitor_bidding_packages_tracking_bidding-package-name": "Bidding Package Name", "monitor_bidding_packages_tracking_address": "Address", "monitor_bidding_packages_tracking_KHLCNT-number": "KHLCNT Number", "monitor_bidding_packages_tracking_date-posting": "Date of posting", "monitor_bidding_packages_tracking_time-bidding-closing": "Time of bidding closing", "monitor_bidding_packages_tracking_bid-price": "<PERSON><PERSON>", "monitor_bidding_packages_tracking_form-bidding-participation": "Form of Bidding Participation", "monitor_bidding_packages_tracking_TBMT-number": "TBMT Number", "monitor_bidding_packages_tracking_group": "Group", "monitor_bidding_packages_tracking_company": "Company", "monitor_bidding_packages_tracking_keyword": "Keyword", "monitor_bidding_packages_tracking_link": "Link", "project-reference_project-type": "Project Type", "project-reference_technology": "Technology", "project-reference_domain": "Domain", "project-reference_project-name": "Project Name", "project-reference_client": "Client", "project-reference_effort": "<PERSON><PERSON><PERSON>", "project-reference_project-manager": "Project Manager", "project-reference_project-description": "Project Description", "project-reference_from-date": "From date", "project-reference_to-date": "To date", "monthly_project_cost_summary_summary": "Summary", "monthly_project_cost_summary_fix-cost": "Fixed Cost", "monthly_project_cost_summary_others": "Others", "monthly_project_cost_summary_department": "Dept.", "monthly_project_cost_summary_month": "Month", "monthly_project_cost_summary_year": "Year", "monthly_project_cost_summary_project-type": "Project Type", "monthly_project_cost_summary_project ": "Projects", "monthly_project_cost_summary_contract-no": "Contract No", "monthly_project_cost_summary_contract-size": "Contract Size", "monthly_project_cost_summary_license": "License", "monthly_project_cost_summary_expense": "Actual Cost", "monthly_project_cost_summary_budget": "Budget", "monthly_project_cost_summary_jan": "Jan", "monthly_project_cost_summary_feb": "Feb", "monthly_project_cost_summary_mar": "Mar", "monthly_project_cost_summary_apr": "Apr", "monthly_project_cost_summary_may": "May", "monthly_project_cost_summary_jun": "Jun", "monthly_project_cost_summary_jul": "Jul", "monthly_project_cost_summary_aug": "Aug", "monthly_project_cost_summary_sep": "Sep", "monthly_project_cost_summary_oct": "Oct", "monthly_project_cost_summary_nov": "Nov", "monthly_project_cost_summary_dec": "Dec", "monthly_project_cost_summary_thirteenth-salary": "13th Salary", "monthly_project_cost_summary_total-cost": "Total Cost (VND)", "monthly_project_cost_summary_remaining-accumulation": "Remaining accumulation", "monthly_project_cost_summary_total": "Total  ", "monthly_project_cost_summary": "Monthly Project Cost", "monthly_project_cost_summary_project-headcount": "Project Headcount", "monthly_project_cost_summary_member-code": "Member Code", "monthly_project_cost_summary_full-name": "Full name", "monthly_project_cost_summary_headcount": "Headcount\t", "monthly_project_cost_summary_from-date": "From date", "monthly_project_cost_summary_to-date": "To date", "monthly_project_cost_summary_no": "No ", "monthly_project_cost_detail_report_detail-report-cost-by-month": "Detail report by month", "monthly_project_cost_detail_report_total-effort-md": "Total effort (Manday)", "monthly_project_cost_detail_report_overhead-allocated-amt": "Overhead Allocated Amt", "monthly_project_cost_detail_report_salary-cost": "Salary Cost", "monthly_project_cost_detail_report_total-cost": "Total Cost (VND)", "monthly_project_cost_detail_report_project": "Year", "monthly_project_cost_detail_report_month": "Month", "monthly_project_cost_detail_report_year": "Projects", "monthly_project_cost_detail_report_department": "Dept.", "monthly_project_cost_detail_report_project-type": "Project Type", "sale-summary-totalRevenueFromPipeline": "Total Revenue from pipeline", "sale-summary-totalVsiiRevenue": "Total VSII Revenue", "sale-summary-totalVsiiServiceSale": "Total VSII services sales", "sale-summary-totalHeadcountVsii": "Total headcount", "on-going-sum": "SUM", "on-going-condition": "CONDITION", "sale-summary-sum": "SUM", "sale-summary-condition": "CONDITION", "all-sale-pipeline-sum": "SUM", "all-sale-pipeline-condition": "CONDITION", "budgeting-plan-sum": "SUM", "budgeting-plan-condition": "CONDITION", "monthly-production-performance-sum": "SUM", "monthly-production-performance-condition": "CONDITION", "resources_in_projects_no": "No", "resources_in_projects_member-code": "Member Code\t", "resources_in_projects_members": "Members", "resources_in_projects_dept": "Dept", "resources_in_projects_main-headcount": "Main Headcount", "resources_in_projects_secondary-headcount        ": "Secondary Headcount\t", "resources_in_projects_non-billable-project": "Non-billable Project", "resources_in_projects_actions": "Actions", "resources_in_projects_year": "Year", "resources_in_projects_weeks": "Weeks", "resources_in_projects_resources-in-projects": "Resources In Projects", "resources_in_projects_Title": "Title", "resources_in_projects_search": "search", "cv-config-add-new": "Add New", "cv-config-technology": "Technology", "cv-config-reference": "Reference", "cv-config-add-technology-config": "Add technology config", "cv-config-skill": "Skill", "cv-config-add-skill": "<PERSON><PERSON>", "cv-config-cancel": "Cancel", "cv-config-add-language-config": "Add language config", "cv-config-language-name": "Language Name", "cv-config-full-name": "Full Name", "cv-config-organization": "Organization", "cv-config-position": "Position", "cv-config-address": "Address", "cv-config-email": "Email", "cv-config-add-reference config": "Add reference config", "cv-config-actions": "Actions", "cv-config-no": "no", "cv-config-last-update": "Last update", "cv-config-user-update": "User update", "cv-config-language": "Language", "cv-config-members": "Members", "cv-config-edit-technology-config": "Edit technology config", "cv-config-submit": "submit", "cv-config-add": "Add", "cv-config-edit-language-config": "Edit language config", "cv-config-phone-number": "Phone", "email-config-add-new": "Add New", "email-config-email-type": "Email Type", "email-config-email-code": "Email Code", "email-config-send-to": "Send To", "email-config-send-cc": "Send CC", "email-config-send-bcc": "Send BCC", "email-config-template": "Template", "email-config-hour": "Hour", "email-config-day-in-month": "Day In Month", "email-config-attachment-file-name": "Attachment File Name", "email-config-subject": "Subject", "email-config-content": "Content", "email-config-status": "Status", "email-config-cancel": "Cancel", "email-config-submit": "Submit", "email-config-add-email-config": "Add <PERSON><PERSON> Config", "email-config-action": "actions", "email-config-edit-email-config": "Edit Email Config", "system-config-edit-config": "Edit Config", "system-config-key": "Key", "system-config-value": "Value", "system-config-note": "Note", "system-config-cancel": "Cancel", "system-config-submit": "Submit", "system-config-action": "actions", "system-config-user-update": "User Update", "system-config-last-update": "Last Update", "manage-rank-edit-rank": "Edit Rank", "manage-rank-rank-name": "Rank name", "manage-rank-note": "Note", "manage-rank-cancel": "Cancel", "manage-rank-submit": "Submit", "manage-rank-action": "Actions", "manage-rank-user-update": "User Update", "manage-rank-last-update": "Last Update", "manage-rank-add": "add", "manage-group-group-code": "Group Code", "manage-group-group-name": "Group Name", "manage-group-search": "Search", "manage-group-add-new": "Add New", "manage-group-group-info": "Group Info", "manage-group-group-type": "Group Type", "manage-group-note": "Note", "manage-group-cancel": "Cancel", "manage-group-submit": "submit", "manage-group-permission": "Permission", "manage-group-add-group": "Add group", "manage-group-edit-group": "Edit group", "manage-group-assigned-user": "Assigned User", "manage-group-action": "Actions", "manage-group-report-name": "Report Name", "manage-group-member-code": "Member Code", "manage-group-user-name": "Username", "manage-group-first-name": "First Name", "manage-group-last-name": "Last Name", "manage-group-title": "Title", "manage-holidays-holiday-type": "Holiday Type", "manage-holidays-filters": "Filters", "manage-holidays-search": "Search", "manage-holidays-add-new": "Add New", "manage-holidays-from-date": "From Date", "manage-holidays-to-date": "To Date", "manage-holidays-note": "Note", "manage-holidays-cancel": "Cancel", "manage-holidays-submit": "Project Info", "manage-holidays-edit-holiday": "Edit holiday", "manage-holidays-add-holiday": "Add holiday", "manage-holidays-action": "Actions", "manage-project-projects": "Projects", "manage-project-project-type": "Project Type", "manage-project-status": "Status", "manage-project-project-manager": "Project Manager", "manage-project-search": "Search", "manage-project-add-new": "Add new", "manage-project-filters": "Filters", "manage-project-download-report": "Download Report", "manage-project-project-info": "Project Info", "manage-project-project-name": "Project Name", "manage-project-dept": "Dept", "manage-project-contract-no": "Contract No", "manage-project-billable": "Billable", "manage-project-start-date": "Start Date", "manage-project-end-date": "End Date", "manage-project-contract-size": "Contract Size", "manage-project-license-amount": "License Amount", "manage-project-cost-limit": "Cost limit", "manage-project-quota": "<PERSON><PERSON><PERSON>", "manage-project-work-completed": "% Work Completed", "manage-project-client": "Client", "manage-project-technology": "Technology", "manage-project-domain": "Domain", "manage-project-effort": "<PERSON><PERSON><PERSON>", "manage-project-project-description": "Project Description", "manage-project-note": "Note", "manage-project-cancel": "Cancel", "manage-project-submit": "Submit", "manage-project-add-project": "Add Project", "manage-project-edit-project": "Edit Project", "manage-project-project-id": "Project ID", "manage-project-add-headcount": "Add headcount", "manage-project-user": "User", "manage-project-member-code": "Member Code", "manage-project-username": "Username", "manage-project-firstname": "Firstname", "manage-project-lastname": "Lastname", "manage-project-headcount": "Headcount", "manage-project-from-date": "From Date", "manage-project-to-date": "To Date", "manage-project-action": "Actions", "manage-project-quota-update-history": "Update quota history", "manage-project-update-date": "Update date", "manage-project-no": "no", "manage-user-member-code": "Member Code", "manage-user-username": "Username", "manage-user-dept": "Dept", "manage-user-contractor": "Contractor", "manage-user-status": "Status", "manage-user-search": "Search", "manage-user-add-new": "Add new", "manage-user-user-info": "User Info", "manage-user-first-name": "First Name", "manage-user-last-name": "Last Name", "manage-user-logtime": "Logtime", "manage-user-customer": "Customer", "manage-user-management-level": "Management level", "manage-user-staff-level": "Staff level", "manage-user-submit": "Submit", "manage-user-group": "Group", "manage-user-on-outboard-info": "On/outboard Info", "manage-user-title": "Title", "manage-user-billable": "Billable", "manage-user-project-permission": "Project Permission", "manage-user-no": "No", "manage-user-onboard-date": "Onboard Date", "manage-user-outboard-date": "Outboard Date", "manage-user-official-business-day": "Official business day", "manage-user-action": "Action", "manage-user-rank": "Rank", "manage-user-from-date": "From date", "manage-user-to-date": "To date", "manage-user-manage-user": "Manage User", "manage-user-filters": "Filters", "manage-user-department": "Department", "manage-user-level": "Level", "manage-special-hours-filters": "Filters", "manage-special-hours-special-hours-type": "Special Hours Type", "manage-special-hours-members": "Members", "manage-special-hours-search": "Search", "manage-special-hours-add-new": "Add New", "manage-special-hours-user": "User", "manage-special-hours-hours-day": "Hours/Day", "manage-special-hours-from-date": "From Date", "manage-special-hours-to-date": "To Date", "manage-special-hours-note": "Note", "manage-special-hours-add-special-hours": "Add Special Hours", "manage-special-hours-cancel": "Cancel", "manage-special-hours-submit": "Submit", "manage-special-hours-edit-special-hours": "Edit Special Hours", "manage-special-hours-no": "No", "manage-special-hours-action": "Actions", "language-config-filter": "Filter", "language-config-add-language": "Add Language", "language-config-language": "Language", "language-config-no": "No", "language-config-screen-name": "Screen's Name", "language-config-default-text-en": "Default Text EN", "language-config-new-text-en": "New Text EN", "language-config-note": "Note", "language-config-actions": "Actions", "language-config-edit-config": "Edit Config", "language-config-other": "Other", "language-config-cancel": "Cancel", "language-config-submit": "Submit", "language-config-style": "Style", "language-config-new-text": "New Text", "language-config-add": "add", "language-config-reset-style": "reset-style", "language-config-add-new": "Add new", "language-config-text-name": "Text Name", "language-config-search": "Search", "flexible-reporting-configuration-filter": "Filter", "flexible-reporting-configuration-text-name": "Text Name", "flexible-reporting-configuration-add": "add", "flexible-reporting-configuration-no": "No", "flexible-reporting-configuration-report-name": "Report Name", "flexible-reporting-configuration-default-text-en": "Default Text EN", "flexible-reporting-configuration-new-text-en": "New Text EN", "flexible-reporting-configuration-note": "Note", "flexible-reporting-configuration-actions": "Actions", "flexible-reporting-configuration-edit-config": "Edit Config", "flexible-reporting-configuration-other": "Other", "flexible-reporting-configuration-cancel": "Cancel", "flexible-reporting-configuration-submit": "Submit", "flexible-reporting-configuration-other-data-sources": "Other data sources", "flexible-reporting-configuration-show": "Show", "flexible-reporting-configuration-sum": "Sum\n", "flexible-reporting-configuration-conditions": "Conditions\n", "flexible-reporting-configuration-style": "Style", "flexible-reporting-configuration-new-text": "New Text", "flexible-reporting-configuration-reset-style": "reset-style", "flexible-reporting-configuration-add-new": "Add new", "column-config-filter": "Filter", "column-config-report-name": "Report Name", "column-config-column-name": "Column name", "column-config-search": "Search", "column-config-no": "No", "column-config-column-name\t": "Column name\t", "column-config-input-type": "Input type", "column-config-calculate": "Calculate", "column-config-actions": "Actions", "column-config-edit-config": "Edit Config", "column-config-note": "Note", "column-config-cancel": "Cancel", "column-config-submit": "Submit", "exchange-rate-config-filter": "Filter", "exchange-rate-config-year": "Year", "exchange-rate-config-currency": "<PERSON><PERSON><PERSON><PERSON>", "exchange-rate-config-search": "search", "exchange-rate-config-add-new": "Add New", "exchange-rate-config-add-exchange-rate-config": "Add exchange rate config", "exchange-rate-config-exchange-rate": "Exchange Rate", "exchange-rate-config-cancel": "Cancel", "exchange-rate-config-edit-exchange-rate-config": "Edit exchange rate config", "exchange-rate-config-no": "No", "exchange-rate-config-actions": "Actions", "exchange-rate-config-submit": "Submit", "manage-department-filter": "Filter", "manage-department-department": "Department", "manage-department-search": "Search", "manage-department-add-new": "Add New", "manage-department-add-department": "Add Department", "manage-department-edit-department": "Edit Department", "manage-department-department-name": "Department Name", "manage-department-cancel": "Cancel", "manage-department-submit": "Submit", "manage-department-last-update": "Last Update", "manage-department-user-update": "User Update", "project-type-config-no": "No", "project-type-config-project-type": "Project Type", "project-type-config-project-type-name": "Project Type Name ", "project-type-config-fixed-cost": "Title Name\t", "project-type-config-last-update": "Last Update", "project-type-config-user-update        ": "User Update", "project-type-config-filters": "Filters", "project-type-config-add": "Add", "project-type-config-search": "Search", "project-type-config-cancel": "Cancel", "project-type-config-submit": "Submit", "project-type-config-edit-project-type-config": "Edit Project Type", "project-type-config-color ": "Color ", "project-type-config-add-project-type-config": "Add Project Type", "project-type-config-project-type-create-billable": "Billable", "title-config-no": "No", "title-config-title": "Title", "title-config-key": "Key", "title-config-title-name": "Title Name\t", "title-config-last-update": "Last Update", "title-config-user-update": "User Update", "title-config-filters": "Filters", "title-config-add": "Add", "title-config-search": "Search", "title-config-cancel": "Cancel", "title-config-submit": "Submit", "title-config-edit-title-config": "Edit Title", "title-config-add-title-config": "Add Title", "non-billables-config-no": "No", "non-billables-config-config-name": "Name", "non-billables-config-key": "Key", "non-billables-config-value": "Value", "non-billables-config-note": "Note", "non-billables-config-last-update": "Last Update", "non-billables-config-user-update": "User Update", "non-billables-config-color": "Color ", "non-billables-config-project-type": "Project Type", "non-billables-config-cancel": "Cancel", "non-billables-config-submit": "Submit", "non-billables-config-edit-config": "Edit config", "register-working-calendar-register-working-calendar": "Register working calendar", "register-working-calendar-filters": "Filters", "register-working-calendar-year": "Year", "register-working-calendar-month": "Month", "register-working-calendar-dept": "Dept.", "register-working-calendar-members": "Members", "register-working-calendar-work-at-office": "Work at office", "register-working-calendar-work-from-home": "Work from home", "register-working-calendar-onsite": "Onsite", "register-working-calendar-full-day-leave": "Full day leave", "register-working-calendar-half-day-leave": "Half day leave", "register-working-calendar-sick-leave": "Sick leave", "register-working-calendar-holiday": "Holiday", "register-working-calendar-wedding-vacation": "Wedding vacation", "register-working-calendar-maternity-leave": "Maternity leave", "register-working-calendar-compensatory-leave": "Compensatory leave", "register-working-calendar-unpaid-leave": "Unpaid leave", "register-working-calendar-over-time": "Over time", "register-working-calendar-total-number-of-employees": "Total number of employees", "register-working-calendar-actions": "Actions", "register-working-calendar-member-code": "Member Code", "register-working-calendar-member-name": "Member Name", "register-working-calendar-title": "Title", "register-working-calendar-status": "Status", "register-working-calendar-late": "Late", "register-working-calendar-early": "Early", "register-working-calendar-tue": "<PERSON><PERSON>", "register-working-calendar-wed": "Wed", "register-working-calendar-thu": "<PERSON>hu", "register-working-calendar-fri": "<PERSON><PERSON>", "register-working-calendar-sat": "Sat", "register-working-calendar-sun": "Sun", "register-working-calendar-mon": "Mon", "register-working-calendar-total": "Total", "register-working-calendar-note": "Note", "register-working-calendar-comments": "Comments", "register-working-calendar-submit": "Submit Note", "register-working-calendar-cancel": "Cancel", "register-working-calendar-working-calendar-closing-date": "Working calendar closing date", "register-working-calendar-closing-date": "Closing date", "register-working-calendar-verify": "Verify", "register-working-calendar-download": "Download Report", "register-working-calendar-copy": "Copy", "register-working-calendar-employee-id": "Employee ID", "register-working-calendar-employee-name": "Employee Name", "register-working-calendar-first-name": "First Name", "register-working-calendar-onsite-days": "Onsite Days", "register-working-calendar-team": "Team", "register-working-calendar-project-name": "Project Name", "register-working-calendar-number-of-os-employees": "Number of OS employees", "register-working-calendar-detail": "Detail", "register-working-calendar-close": "Close", "register-working-calendar-search": "search", "register-working-calendar-department": "department", "register-working-calendar-message-working-calendar-detail": "You signed up for WFH for the week, please provide a reason to continue. An administrator will review this request.", "register-working-calendar-message-working-calendar-verify": "Reason registered WFH for the week", "register-working-calendar-declines": "declines", "register-working-calendar-message": "message", "register-working-calendar-approve": "approve", "manage-leave-days-members": "Members", "manage-leave-days-no": "No", "manage-leave-days-member-code": "Member code        ", "manage-leave-days-title": "Title        ", "manage-leave-days-dept": "Debpt.", "manage-leave-days-total-leave-days": "total leave days        ", "manage-leave-days-number-of-leave-days-used": "Number of leave days used", "manage-leave-days-remaining-leave-days": "Remaining leave days        ", "manage-leave-days-actions": "Actions", "manage-leave-days-search": "Search", "manage-leave-days-filters": "Filters", "manage-leave-days-manage-leave-days": "Manage leave days", "manage-resignation-year": "Year", "manage-resignation-members": "Members", "manage-resignation-dept": "Dept", "manage-resignation-status": "Status", "manage-resignation-no": "No\t", "manage-resignation-approver": "Approver", "manage-resignation-add": "Add", "manage-resignation-title": "Title", "manage-resignation-from-date": "From-Date", "manage-resignation-approved-date": "Approved-Date        ", "manage-resignation-actions": "Actions", "manage-resignation-search": "Search", "manage-resignation-filters": "Filters", "manage-resignation-manage-resignation": "Manage resignation", "manage-resignation-reason": "reason", "manage-resignation-submit": "submit", "manage-resignation-cancel": "cancel", "weekly-effort-no": "No", "weekly-effort-members": "Members", "weekly-effort-member-code": "Member Code\t", "weekly-effort-level": "Level", "weekly-effort-dept": "Dept", "weekly-effort-effort-in-week-hours       ": "Effort in week (hours)", "weekly-effort-difference-hours": "Difference hours\t", "weekly-effort-projects": "Projects", "weekly-effort-year": "Year", "weekly-effort-weeks": "Weeks", "weekly-effort-timesheet-status": "Timesheet status", "weekly-effort-total-efforts-manhours": "Total efforts (Manhours)", "weekly-effort-level 1": "Level 1", "weekly-effort-level 2": "Level 2\n", "weekly-effort-level 3": "Level 3", "weekly-effort-level 4": "Level 4", "weekly-effort-level 0": "Level 0", "weekly-effort-level -1": "Level -1", "weekly-effort-total-cost-vnd": "Total Cost (VND)", "weekly-effort-project-detail": "Project Detail", "weekly-effort-pm-verifed": "PM verifed", "weekly-effort-pm-verified-date": "PM verified date", "weekly-effort-qa-verifed": "QA verifed", "weekly-effort-qa-verified-date": "QA verified date", "weekly-effort-effort-pm-verified": "Effort PM verified", "weekly-effort-effort-in-week-hours": "Effort in week (hours)", "weekly-effort-payable-ot-verified": "Payable OT verified", "weekly-effort-payable-ot": "Payable OT", "weekly-effort-non-payable-ot-verified\n": "Non payable OT verified \n", "weekly-effort-non-payable-ot": "Non payable OT", "weekly-effort-comments": "Comments", "weekly-effort-download-report": "download-report", "weekly-effort-search": "search", "weekly-effort-department": "department", "cost-effort-monitoring-monthly-monitoring-contract-size": "Contract Size", "cost-effort-monitoring-monthly-monitoring-license": "License", "cost-effort-monitoring-monthly-monitoring-cost-limit": "Cost limit", "cost-effort-monitoring-monthly-monitoring-actual-cost": "Actual cost", "cost-effort-monitoring-monthly-monitoring-remaining-cost": "Remaining Cost", "cost-effort-monitoring-monthly-monitoring-time": "Time", "cost-effort-monitoring-monthly-monitoring-accumulated-cost": "Accumulated Cost", "cost-effort-monitoring-monthly-monitoring-fixed-cost": "Fixed Cost", "cost-effort-monitoring-monthly-monitoring-others": "Others", "cost-effort-monitoring-monthly-monitoring-comments": "Comments", "cost-effort-monitoring-monthly-monitoring-year": "Year", "cost-effort-monitoring-monthly-monitoring-month": "Month", "cost-effort-monitoring-monthly-monitoring-project": "Projects", "cost-effort-monitoring-monthly-monitoring-effort-quota": "<PERSON><PERSON><PERSON>", "cost-effort-monitoring-monthly-monitoring-remaining-effort": "<PERSON><PERSON><PERSON>", "cost-effort-monitoring-monthly-monitoring-search": "Search", "cost-effort-monitoring-monthly-monitoring-cost-statistics": "Cost statistics (VND)", "cost-effort-monitoring-monthly-monitoring-cost-chart": "Cost chart in", "cost-effort-monitoring-monthly-monitoring-statistic-effort": "Effort statistic (manday)", "cost-effort-monitoring-monthly-monitoring-actual-effort": "Actual Effort", "cost-effort-monitoring-monthly-monitoring-accumulated-effort": "Accumulated <PERSON><PERSON><PERSON>", "cost-effort-monitoring-monthly-monitoring-effort-chart": "Effort chart in", "cost-effort-monitoring-monthly-monitoring-monthly-monitoring": "Monthly Monitoring", "cost-effort-monitoring-monthly-monitoring-download": "download", "cost-effort-monitoring-weekly-monitoring-year": "Year", "cost-effort-monitoring-weekly-monitoring-weeks": "Weeks", "cost-effort-monitoring-weekly-monitoring-projects": "Projects", "cost-effort-monitoring-weekly-monitoring-estimated-cost": "Estimated cost", "cost-effort-monitoring-weekly-monitoring-effort-md": "<PERSON><PERSON><PERSON> (md)", "cost-effort-monitoring-weekly-monitoring-cost-vnd": "Cost-VND", "cost-effort-monitoring-weekly-monitoring-effort": "<PERSON><PERSON><PERSON>", "cost-effort-monitoring-weekly-monitoring-search": "search", "cost-effort-monitoring-weekly-monitoring-cost-statistics": "Cost statistics (VND)", "cost-effort-monitoring-weekly-monitoring-estimated-chart": "Weekly effort and cost correlation chart", "non-billable-monitoring-nbm-by-member-no": "Weeks", "non-billable-monitoring-nbm-by-member-title": "Title\t", "non-billable-monitoring-nbm-by-member-total-efforts-manhours": "Total efforts (Manhours)\t", "non-billable-monitoring-nbm-by-member-dept": "Dept", "non-billable-monitoring-nbm-by-member-projects": "Projects\t", "non-billable-monitoring-nbm-by-member-members": "Members", "non-billable-monitoring-nbm-by-member-not-enough-timesheets-ratio": "Not Enough Timesheets ratio        ", "non-billable-monitoring-nbm-by-member-billables-projects": "Billables Projects", "non-billable-monitoring-nbm-by-member-actions": "Actions", "non-billable-monitoring-nbm-by-member-timesheet-status": "Timesheet status\n", "non-billable-monitoring-nbm-by-member-year": "Year", "non-billable-monitoring-nbm-by-member-weeks": "Weeks", "non-billable-monitoring-nbm-by-member-warning-nbm-member": "Warning nbm member", "non-billable-monitoring-nbm-by-member-nbm-consecutive-week": "NBM Consecutive Week", "non-billable-monitoring-nbm-by-member-comments": "Comments", "non-billable-monitoring-nbm-by-member-nbm-by-member": "NBM By Member", "non-billable-monitoring-nbm-by-member-search": "search", "non-billable-monitoring-nbm-by-member-download-report": "download-report", "non-billable-monitoring-nbm-by-member-commnents": "commnents", "non-billable-monitoring-nbm-by-member-member-code": "Member Code\t", "non-billable-monitoring-nbm-ratio-chart-weeks": "Weeks", "non-billable-monitoring-nbm-ratio-chart-year": "Year", "non-billable-monitoring-nbm-ratio-chart-non-billables-percentage": "Non billables Percentage", "non-billable-monitoring-nbm-ratio-chart-search": "search", "non-billable-monitoring-nbm-ratio-chart-download-report": "download-report", "general-report-project-no": "No", "general-report-project-project-name": "Project Name", "general-report-project-project-manager": "Project Manager\t", "general-report-project-project-implementation-phase": "Project Implementation Phase", "general-report-project-project-progress-assessment": "Project Progress Assessment", "general-report-project-dept": "Dept", "general-report-project-project-type": "Project Type", "general-report-project-start-date": "Start Date", "general-report-project-end-date": "End Date", "general-report-project-user-update": "User Update", "general-report-project-last-update": "Last Update", "general-report-project-actions": "Actions", "general-report-project-year": "Year", "general-report-project-month": "Month", "general-report-project-projects": "Projects", "general-report-project-search": "search", "general-report-project-edit-project-report": "Edit Project Report", "general-report-project-add-project-report": "Add Project Report", "general-report-project-project-info": "Project Info", "general-report-project-monthly-report": "Monthly Report", "general-report-project-progress-milestone": "Progress & Milestone", "general-report-project-issue-risk": "Issue & Risk", "general-report-project-resource": "resource", "general-report-project-sale-up-sales": "\"Sale & Up-Sales", "general-report-project-next-plan": "Next Plan", "general-report-project-cancel": "cancel", "general-report-project-submit": "submit", "general-report-project-milestone-list-approved-by-bod-customer": "Milestone List approved by BOD/ Customer", "general-report-project-work-completed": "% Work Completed", "general-report-project-milestone": "Milestone", "general-report-project-date": "Date", "general-report-project-release-package": "Release Package", "general-report-project-status": "Status", "general-report-project-finished-main-tasks-in-month": "Finished Main Tasks in Month", "general-report-project-delayed-not-finished": "Delayed/ Not finished vs Plan in Month", "general-report-project-completed-milestones": "Completed Milestones", "general-report-project-next-milestone": "Next Milestone", "general-report-project-desciption": "desciption", "general-report-project-type": "type", "general-report-project-root-cause": "Root Cause", "general-report-project-proposed-solution": "Proposed Solution", "general-report-project-total-headcounts": "Total headcounts", "general-report-project-resource-review": "Resource review", "general-report-project-oppotunities-expand": "New oppotunities/expand request", "general-report-project-change-of-sale": "Change of Sale", "general-report-project-potential-revenue": "Potential Revenue", "general-report-project-comment": "comment", "general-report-project-task-name": "Task name", "general-report-project-tentative-start-date": "Tentative Start Date", "general-report-project-due-date": "Due Date", "general-report-product-no": "No", "general-report-product-project": "Project", "general-report-product-sprint": "Sprint", "general-report-product-completed": "% Completed", "general-report-product-total-efforts-hours": "Total Efforts (hours)", "general-report-product-start-date": "Start Date", "general-report-product-end-date": "End Date", "general-report-product-current-sprint-cost": "Current Sprint Cost", "general-report-product-total-project-cost": "Total Project Cost", "general-report-product-synchronize": "synchronize", "general-report-product-search": "search", "general-report-orm-no": "No", "general-report-orm-report-name": "Report Name", "general-report-orm-month": "Month", "general-report-orm-dept": "Dept", "general-report-orm-upload-user": "Upload User", "general-report-orm-last-update": "Last Update", "general-report-orm-year": "year", "general-report-orm-search": "search", "general-report-orm-department": "department", "general-report-orm-add-new-report": "Add New Report", "general-report-orm-submit": "submit", "general-report-orm-file": "file", "general-report-orm-attachment": "Attachment", "general-report-orm-cancel": "cancel", "general-report-orm-add-new": "Add New", "resources-in-projects-no": "No", "resources-in-projects-member-code": "Member Code\t", "resources-in-projects-members": "Members", "resources-in-projects-dept": "Dept", "resources-in-projects-main-headcount": "Main Headcount", "resources-in-projects-secondary-headcount": "Secondary Headcount\t", "resources-in-projects-non-billable-project": "Non-billable Project", "resources-in-projects-actions": "Actions", "resources-in-projects-year": "Year", "resources-in-projects-weeks": "Weeks", "resources-in-projects-resources-in-projects": "Resources In Projects", "resources-in-projects-title": "Title", "resources-in-projects-search": "search", "resources-in-projects-comments": "Comments", "resources-in-projects-download": "Download", "skill-manager-skill-report-no": "no", "skill-manager-skill-report-member-code": "Member Code        ", "skill-manager-skill-report-members": "Members", "skill-manager-skill-report-dept": "Dept", "skill-manager-skill-report-status": "Status", "skill-manager-skill-report-actions": "Actions", "skill-manager-skill-report-title": "Title", "skill-manager-skill-report-skill-name": "Skill Name\t", "skill-manager-skill-report-skill-level": "Skill Level\t", "skill-manager-skill-report-skill-experience": "Skill Experience", "skill-manager-skill-report-degree": "Degree", "skill-manager-skill-report-search": "search", "skill-manager-skill-report-download": "Download Report", "skill-manager-skill-update-no": "no", "skill-manager-skill-update-member-code": "Member Code\t", "skill-manager-skill-update-members": "Members", "skill-manager-skill-update-dept": "Dept", "skill-manager-skill-update-status": "Status", "skill-manager-skill-update-actions": "Actions", "skill-manager-skill-update-title": "Title", "skill-manager-skill-update-search": "Search", "skill-manager-skill-update-add-new": "Add New", "monthly-project-cost-detail-report-detail-report-cost-by-month": "Detail report by month", "monthly-project-cost-detail-report-total-effort-md": "Total effort (Manday)", "monthly-project-cost-detail-report-overhead-allocated-amt": "Overhead Allocated Amt", "monthly-project-cost-detail-report-salary-cost": "Salary Cost", "monthly-project-cost-detail-report-total-cost": "Total Cost (VND)", "monthly-project-cost-detail-report-year": "Year", "monthly-project-cost-detail-report-month": "Month", "monthly-project-cost-detail-report-project": "Projects", "monthly-project-cost-detail-report-department": "Dept.", "monthly-project-cost-detail-report-project-type": "Project Type", "monthly-project-cost-detail-report-search": "search", "monthly-project-cost-monthly-cost-year": "year", "monthly-project-cost-monthly-cost-month": "month", "monthly-project-cost-monthly-cost-project": "projects", "monthly-project-cost-monthly-cost-project-type": "project type", "monthly-project-cost-monthly-cost-overhead-allocated-amt": "Overhead Allocated Amount", "monthly-project-cost-monthly-cost-salary-cost": "Salary Cost", "monthly-project-cost-monthly-cost-total-cost": "Total Cost (VND)", "monthly-project-cost-monthly-cost-created": "created", "monthly-project-cost-monthly-cost-creator": "creator", "monthly-project-cost-monthly-cost-actions": "actions", "monthly-project-cost-monthly-cost-search": "search", "monthly-project-cost-monthly-cost-user-update": "User Update", "monthly-project-cost-monthly-cost-last-update": "Last Update", "monthly-project-cost-monthly-cost-monthly-cost": "Monthly Cost", "monthly-project-cost-monthly-cost-note": "note", "monthly-project-cost-monthly-cost-cancel": "cancel", "monthly-project-cost-monthly-cost-submit": "submit", "monthly-project-cost-monthly-cost-add-new": "Add New", "monthly-project-cost-monthly-cost-download": "download report", "monthly-project-cost-summary-summary": "Summary", "monthly-project-cost-summary-fix-cost": "Fixed Cost", "monthly-project-cost-summary-others": "Others", "monthly-project-cost-summary-department": "Dept.", "monthly-project-cost-summary-month": "Month", "monthly-project-cost-summary-year": "Year", "monthly-project-cost-summary-project-type": "Project Type", "monthly-project-cost-summary-project": "Projects", "monthly-project-cost-summary-contract-no": "Contract No", "monthly-project-cost-summary-contract-size": "Contract Size", "monthly-project-cost-summary-license": "License", "monthly-project-cost-summary-expense": "Actual Cost", "monthly-project-cost-summary-budget": "Budget", "monthly-project-cost-summary-jan": "Jan", "monthly-project-cost-summary-feb": "Feb", "monthly-project-cost-summary-mar": "Mar", "monthly-project-cost-summary-apr": "Apr", "monthly-project-cost-summary-may": "May", "monthly-project-cost-summary-jun": "Jun", "monthly-project-cost-summary-jul": "Jul", "monthly-project-cost-summary-aug": "Aug", "monthly-project-cost-summary-sep": "Sep", "monthly-project-cost-summary-oct": "Oct", "monthly-project-cost-summary-nov": "Nov", "monthly-project-cost-summary-dec": "Dec", "monthly-project-cost-summary-thirteenth-salary": "13th Salary", "monthly-project-cost-summary-total-cost": "Total Cost (VND)", "monthly-project-cost-summary-remaining-accumulation": "Remaining accumulation", "monthly-project-cost-summary-total": "Total  ", "monthly-project-cost-summary-monthly-project-cost": "Monthly Project Cost", "monthly-project-cost-summary-project-headcount": "Project Headcount", "monthly-project-cost-summary-member-code": "Member Code", "monthly-project-cost-summary-full-name": "Full name", "monthly-project-cost-summary-headcount": "Headcount\t", "monthly-project-cost-summary-from-date": "From date", "monthly-project-cost-summary-to-date": "To date", "monthly-project-cost-summary-no": "No ", "monthly-project-cost-summary-search": "search", "project-reference-project-type": "Project Type", "project-reference-technology": "Technology", "project-reference-domain": "Domain", "project-reference-project-name": "Project Name", "project-reference-client": "Client", "project-reference-effort": "<PERSON><PERSON><PERSON>", "project-reference-project-manager": "Project Manager", "project-reference-project-description": "Project Description", "project-reference-from-date": "From date", "project-reference-to-date": "To date", "project-reference-search": "Search", "project-reference-no": "no", "project-reference-download": "download", "monitor-bidding-packages-tracking-bidding-tracking": "Bidding Tracking", "monitor-bidding-packages-tracking-type": "Type", "monitor-bidding-packages-tracking-bidding-package-name": "Bidding Package Name", "monitor-bidding-packages-tracking-address": "Address", "monitor-bidding-packages-tracking-KHLCNT-number": "KHLCNT Number", "monitor-bidding-packages-tracking-date-posting": "Date of posting", "monitor-bidding-packages-tracking-time-bidding-closing": "Time of bidding closing", "monitor-bidding-packages-tracking-bid-price": "<PERSON><PERSON>", "monitor-bidding-packages-tracking-form-bidding-participation": "Form of Bidding Participation", "monitor-bidding-packages-tracking-TBMT-number": "TBMT Number", "monitor-bidding-packages-tracking-group": "Group", "monitor-bidding-packages-tracking-company": "Company", "monitor-bidding-packages-tracking-keyword": "Keyword", "monitor-bidding-packages-tracking-link": "Link", "monitor-bidding-packages-tracking-search": "search", "monitor-bidding-packages-tracking-no": "no", "monitor-bidding-packages-tracking-synchronize": "synchronize", "monitor-bidding-packages-tracking-download": "Download Report", "monitor-bidding-packages-report": "Monitor Bidding Packages", "monitor-bidding-packages-report-bidding-report": "Bidding Report", "monitor-bidding-packages-report-type": "Type", "monitor-bidding-packages-report-bidding-package-name": "Bidding Package Name", "monitor-bidding-packages-report-address": "Address", "monitor-bidding-packages-report-status": "Status", "monitor-bidding-packages-report-KHLCNT-number": "KHLCNT Number", "monitor-bidding-packages-report-budget": "Budget", "monitor-bidding-packages-report-date-posting": "Date of posting", "monitor-bidding-packages-report-time-bidding-closing": "Time of bidding closing", "monitor-bidding-packages-report-bid-price": "<PERSON><PERSON>", "monitor-bidding-packages-report-form-bidding-participation": "Form of Bidding Participation", "monitor-bidding-packages-report-TBMT-number": "TBMT Number", "monitor-bidding-packages-report-group": "Group", "monitor-bidding-packages-report-company": "Company", "monitor-bidding-packages-report-keyword": "Keyword", "monitor-bidding-packages-report-comment": "Comment", "monitor-bidding-packages-report-link": "Link", "monitor-bidding-packages-report-add-bidding-packages": "Add bidding packages", "monitor-bidding-packages-report-edit-bidding-packages": "edit packages", "monitor-bidding-packages-report-submit": "submit", "monitor-bidding-packages-report-cancel": "cancel", "monitor-bidding-packages-report-search": "search", "monitor-bidding-packages-report-no": "no", "monitor-bidding-packages-report-download": "Download Report", "sales-lead-supplier-supplier-name": "Supplier Name", "sales-lead-supplier-from-date": "From date", "sales-lead-supplier-to-date": "To date", "sales-lead-supplier-pic-user-name": "PIC", "sales-lead-supplier-technology": "Technology", "sales-lead-supplier-quantity": "Quantity", "sales-lead-supplier-unit-price": "Unit Price", "sales-lead-supplier-work-type": "Work Type", "sales-lead-supplier-note": "Note", "sales-lead-supplier-supplier-checking": "Supplier Checking", "sales-lead-supplier-edit-supplier": "Edit Supplier", "sales-lead-supplier-remote": "Remote", "sales-lead-supplier-onsite": "Onsite", "sales-lead-supplier-add-supplier": "Add Supplier", "sales-lead-supplier-action": "Actions", "sales-lead-supplier-search": "search", "sales-lead-supplier-submit": "submit", "sales-lead-supplier-cancel": "cancel", "sales-lead-supplier-no": "no", "sales-lead-supplier-download": "download report", "sales-lead-requests-sales-lead": "Sales lead", "sales-lead-requests-requests-checking": "Requests Checking", "sales-lead-requests-partner-name": "Partner Name", "sales-lead-requests-received-date": "Received Date", "sales-lead-requests-status": "Status", "sales-lead-requests-pic-user-name": "PIC", "sales-lead-requests-not-start": "Not Start", "sales-lead-requests-inprogress": "Inproress", "sales-lead-requests-stop": "Stop", "sales-lead-requests-request": "Request", "sales-lead-requests-technology": "Technology", "sales-lead-requests-quantity": "Quantity", "sales-lead-requests-timeline": "Timeline", "sales-lead-requests-possibility": "Possibility\t", "sales-lead-requests-domain": "Domain", "sales-lead-requests-note": "Note", "sales-lead-requests-add-request": "Add Request", "sales-lead-requests-high": "High", "sales-lead-requests-normal": "Normal", "sales-lead-requests-low": "Low", "sales-lead-requests-edit-request": "Edit Request", "sales-lead-requests-no": "No", "sales-lead-requests-action": "Actions", "sales-lead-requests-submit": "submit", "sales-lead-requests-cancel": "cancel", "sales-lead-requests-download": "download", "sales-lead-requests-search": "search", "all-sale-pipeline-search-mode": "Search Mode", "menu-dashboard": "Dashboard", "menu-administration": "Administration", "menu-manage-user": "Manage user", "menu-manage-project": "Manage project", "menu-manage-holiday": "Manage holidays", "menu-manage-special-hours": "Manage special hours", "menu-manage-group": "Manage group", "menu-manage-rank": "Manage rank", "menu-system-config": "System config", "menu-email-config": "<PERSON><PERSON> config", "menu-cv-config": "CV config", "menu-exchange-rate-config": "Exchange rate config", "menu-manage-department": "Manage department", "menu-project-type-config": "Project type config", "menu-title-config": "Title config", "menu-non-billables-config": "Non-billables Config", "menu-flexible-reporting": "Flexible Reporting", "menu-column-config": "Column Config", "menu-flexible-reporting-config": "Flexible Reporting Configuration", "menu-language-config": "Language Config", "menu-reports": "Reports", "menu-general-report": "General Report", "menu-orm-report": "ORM Report", "menu-product-report": "Product Report", "menu-project-report": "Project Report", "menu-weekly-effort": "Weekly Effort", "menu-monthly-effort": "Monthly effort", "menu-monthly-effort-summary": "Summary", "menu-monthly-effort-project": "Effort by project", "menu-monthly-effort-department-member": "Effort by member", "menu-non-billable-monitoring": "Non-billables Monitoring", "menu-non-billable-by-member": "NBM by Member", "menu-nonbill-ratio-chart": "NBM Ratio Chart", "menu-resources-in-project": "Resources In Projects", "menu-cost-effort-monitoring": "Cost & Effort Monitoring", "menu-weekly-monitoring": "Weekly Monitoring", "menu-monthly-monitoring": "Monthly Monitoring", "menu-monthly-project-cost": "Monthly Project Cost", "menu-monthly-project-cost-summary": "Summary", "menu-detail-report-cost-by-month": "Detail report by month", "menu-monthly-cost-data": "Monthly Cost", "menu-sales-report": "Sales report", "menu-monthly-production-performance": "Monthly production performance", "menu-sales-pipeline": "Sales pipeline", "menu-sales-pipeline-summary": "Summary", "menu-on-going": "On-going", "menu-sale-salepipeline-bidding": "All sales pipeline", "menu-budgeting-plan": "Budgeting Plan", "menu-sales-lead": "Sales lead", "menu-monitor-bidding-package": "Monitor Bidding Packages", "menu-project-reference": "Project Reference", "menu-manage-skills": "Manage Skills", "menu-skills-update": "Skills Update", "menu-skills-report": "Skills Report", "menu-working-calender": "Working Calender", "menu-hr-working-calendar": "Register working calendar", "menu-manage-leave-days": "Manage leave days", "menu-manage-leaves": "Manage leaves", "menu-manage-resignation": "Manage resignation", "menu-payment-status": "Payment status of project", "dashboard-welcome": "Welcome back,", "dashboard-system-title": "VSII REPORTING SYSTEM", "dashboard-accessibility-content": "Online reporting systems can be accessed from anywhere with an internet connection, making it easy for users to submit and view reports at any time.", "dashboard-efficiency-content": "Online reporting systems can streamline the reporting process by automating certain tasks, reducing the need for manual data entry and minimizing the risk of errors.", "dashboard-efficiency": "Efficiency:", "dashboard-real-time": "Real-time updates:", "dashboard-real-time-content": "Online reporting systems can provide real-time updates on the status of reports, allowing users to track their progress and make informed decisions.", "monitor-bidding-packages-edit-bidding-packages": "Monitor Bidding Packages Edit", "monthly-effort-summary-year": "Year", "monthly-effort-summary-month": "Month", "monthly-effort-summary-commnents": "commnents", "monthly-effort-summary-download-report": "download-report", "monthly-effort-summary-update-effort-plan": "Update effort plan", "monthly-effort-summary-no-send-report": "Number of projects haven't sent report in month", "monthly-effort-summary-number-of-projects": "Projects Statistics of Departments", "monthly-effort-summary-effort-plan": "Effort plan", "monthly-effort-summary-sent-monthly": "Sent monthly report", "monthly-effort-summary-not-sent-monthly": "Have not sent monthly reports", "monthly-effort-summary-show-chart": "Show", "monthly-effort-summary-submit": "submit", "monthly-effort-summary-cancel": "cancel", "monthly-effort-summary-creation-and-log-time": "Redmine logtime statistics", "monthly-effort-summary-logtime-ratio-chart": "Logtime Ratio Chart", "monthly-effort-summary-effort-allocation": "Effort distribution statistics by project type", "monthly-effort-summary-type": "type", "monthly-effort-summary-effort-md": "<PERSON><PERSON><PERSON> (md)", "monthly-effort-summary-search": "search", "monthly-effort-member-year": "Year", "monthly-effort-member-month": "Month", "monthly-effort-member-dept": "Dept", "monthly-effort-member-project-type": "project type", "monthly-effort-member-projects": "Projects\t", "monthly-effort-member-download-report": "download-report", "monthly-effort-member-submit": "submit", "monthly-effort-member-no": "no", "monthly-effort-member-department": "department", "monthly-effort-member-timesheet-status": "Timesheet status\n", "monthly-effort-member-members": "Members", "monthly-effort-member-search": "search", "monthly-effort-member-member-code": "Member Code", "monthly-effort-member-level": "Level", "monthly-effort-member-difference-hours": "Difference hours", "monthly-effort-member-effort-in-month": "Effort in month (hours)", "monthly-effort-member-total-effort": "Total efforts (Manhours)", "monthly-effort-project-year": "Year", "monthly-effort-project-month": "Month", "monthly-effort-project-dept": "Dept", "monthly-effort-project-project-type": "project type", "monthly-effort-project-projects": "Projects\t", "monthly-effort-project-download-report": "download-report", "monthly-effort-project-billable": "billable", "monthly-effort-project-submit": "submit", "monthly-effort-project-jan": "Jan", "monthly-effort-project-feb": "Feb", "monthly-effort-project-mar": "Mar", "monthly-effort-project-apr": "Apr", "monthly-effort-project-may": "May", "monthly-effort-project-jun": "Jun", "monthly-effort-project-jul": "Jul", "monthly-effort-project-aug": "Aug", "monthly-effort-project-sep": "Sep", "monthly-effort-project-oct": "Oct", "monthly-effort-project-nov": "Nov", "monthly-effort-project-dec": "Dec", "monthly-effort-project-thirteenth-salary": "13th Salary", "monthly-effort-project-department": "department", "monthly-effort-project-work-completed": "% Work Completed", "monthly-effort-project-total-quota": "total-quota", "monthly-effort-project-previous-effort": "Previous Effort", "monthly-effort-project-total-used-effort": "Total Used Effort", "monthly-effort-project-remaining-effort": "<PERSON><PERSON><PERSON>", "monthly-effort-project-search": "search", "sale-summary-year": "Year", "sale-summary-download-report": "Download Report", "manage-holidays-no": "No", "manage-group-no": "No", "manage-rank-no": "No", "email-config-no": "No", "system-config-no": "No", "manage-department-no": "No", "manage-user-edit-user": "Edit User", "manage-user-cancel": "Cancel", "monthly-production-performance-search": "Search", "monthly-production-performance-download-report": "Download Report", "monthly-effort-summary-send-report": "Send report", "monthly-project-cost-detail-report-dowload": "Download Report", "non-billable-monitoring-enough-timesheet": "Enough timesheet", "default-text": "Default Text EN", "new-text": "New Text", "actions": "Actions", "language-config": "Language Config", "=======================MANAGE LEAVE DAYS =======================": "MANAGE LEAVE DAYS INFO", "leave-days-title": "Leave days", "leave-days-remaining": "Remaining leave days", "leave-days-remaining-total": "Total leave days last year", "leave-days-seniority": "Seniority leave days", "leave-days-compensation": "Compensation leave days", "leave-days-annual": "Annual leave days", "leave-days-sick": "Sick leave days", "leave-days-unpaid": "Unpaid leave days", "leave-days-other": "Other leave types", "leave-days-wedding": "Wedding leave", "leave-days-funeral": "Funeral leave", "leave-days-maternity": "Maternity leave", "number-days": "{leaveType} days", "reject-leave-request": "Reject leave request", "send-request": "Send", "total-leave-day": "Total leave days", "leave-from-date": "Leave from date", "leave-to-date": "Leave to date", "reject-reason": "Reject reason", "enter-reject-reason": "Enter reject reason...", "leaves-does-not-exist": "Ticket does not exist", "update-leaves-success": "Update success", "create-Leaves-success": "Create success", "save-or-update-leaves-exception": "Save or update exception", "delete-leaves-success": "Delete success", "can-not-access-rights": "Can not access rights", "leave-have-been-approved-or-rejected": "Ticket has been approved or rejected", "reject-leaves-success": "Reject Ticket success", "confirm-approve-leaves": "Do you approve this leave request?", "=======================MANAGE LEAVE =======================": "MANAGE LEAVE ", "manage-leaves-requests": "Manage leaves", "manage-leaves-add": "Add leaves", "manage-leaves-add-leave": "Add Leave", "manage-leaves-edit": "Edit leaves", "manage-leaves-type": "Leaves type", "manage-leaves-approve-confirm": "Approve member leave request?", "manage-leaves-approve-success": "Approve success", "manage-leaves-decline-success": "Decline success", "manage-leaves-delete-confirm": "Are you sure you want to delete this leave request?", "manage-leaves-request-date": "Request Day", "manage-leaves-number-of-days": "Number of days", "manage-leaves-direct-manager": "Line Manager", "manage-leaves-next-manager": "Dept. Head", "leave-sequence": "From date must be after To date the previous leave", "manage-leaves-days-exhausted": "You have used up all your available leave days.", "manage-leaves-annual-full-exceed-limit": "You cannot apply for more than 2 consecutive full-day annual leaves.", "manage-leaves-annual-full-not-enough": "Insufficient annual leave balance for a full-day request.", "manage-leaves-annual-exceed-limit": "Only up to 1 day of annual leave is allowed per request.", "manage-leaves-annual-not-enough": "You do not have enough leave days for request.", "manage-leaves-sick-full-exceed-limit": "The number of sick leave days requested exceeds the allowed limit per application.", "manage-leaves-sick-full-over-total": "You have exceeded the total allowed sick leave days for the year.", "manage-leaves-sick-half-exceed-limit": "Only one half-day sick leave is allowed per application.", "manage-leaves-sick-half-not-enough": "Insufficient sick leave balance or request exceeds the allowed limit.", "manage-leaves-special-exceed-limit": "You cannot request more than 3 days of special leave at once.", "manage-leaves-half-dayoff-exceeded": "You have used up all your available half-day leave days.", "manage-leaves-half-dayoff-limit": "Leave type is not chosen other days off", "manage-leaves-request-error": "Leave request error", "manage-leaves-email-not-found": "Line manager not found", "manage-leaves-invalid-selection": "Invalid selection", "manage-leaves-full-name": "Full Name", "manage-leaves-member-code": "Member Code", "manage-leaves-position": "Position", "manage-leaves-group": "Group", "manage-leaves-contract-type": "Contract Type", "manage-leaves-total-days": "Total Leave Days", "manage-leaves-note": "Note", "manage-leaves-add-action": "Add", "manage-leaves-edit-action": "Edit", "manage-leaves-delete-action": "Delete", "manage-leaves-from-date": "السعودية", "manage-leaves-to-date": "السعودية", "manage-leaves-members": "Members", "manage-leaves-dept": "Dept.", "manage-leaves-leaves-type": "Leaves-Type", "manage-leaves-status": "Status", "manage-leaves-no": "No", "manage-leaves-approver": "Approver", "manage-leaves-approve": "Approve", "manage-leaves-decline": "Decline", "manage-leaves-actions": "Actions", "manage-leaves-search": "Search", "manage-leaves-approved-date": "Approved-Date", "manage-leaves-filters": "Filters", "manage-leaves-manage-leaves": "Manage-Leaves", "manage-leaves-reason": "reason", "manage-leaves-submit": "submit", "manage-leaves-send": "send", "manage-leaves-cancel": "cancel", "manage-leaves-total-leave-days": "Total Leave Days", "manage-leaves-add-leaves": "Add Leaves", "manage-leaves-leave-request": "Leave Request", "manage-leaves-you-are-not-hr": "You are not HR", "manage-leaves-delete-leave-request": "Delete leave request", "manage-leaves-delete": "Delete", "manage-leaves-days-wedding": "Wedding leave", "manage-leaves-days-funeral": "Funeral leave", "manage-leaves-days-maternity": "Maternity leave", "manage-leaves-remaining-total": "Total leave days last year", "manage-leaves-seniority": "Seniority leave days", "manage-leaves-compensation": "Compensation leave days", "manage-leaves-annual": "Annual leave days", "manage-leaves-sick": "Sick leave days", "manage-leaves-unpaid": "Unpaid leave days", "manage-leaves-other": "Other leave types", "========================OVERTIME REPORT=======================": "OVERTIME REPORT", "manage-ot-modal-title": "Add overtime report", "manage-ot-department": "Department", "manage-ot-direct-manager": "Direct manager", "manage-ot-next-manager": "Next manager", "manage-ot-reason": "Overtime reason", "manage-ot-project-name": "Project name", "manage-ot-other-reason": "Other reason", "manage-ot-detail": "Detail", "manage-ot-from": "Overtime from", "manage-ot-to": "Overtime to", "manage-ot-hours": "Overtime hours", "manage-ot-total-hours": "Total overtime hours", "manage-ot-total-compensate-hours": "Total compensate hours", "manage-ot-equivalent-days": "Equivalent days", "manage-ot-compensate-type": "Compensatory leave", "manage-ot-compensate-from": "Compensatory from", "manage-ot-compensate-to": "Compensatory to", "manage-ot-delete-confirm": "Are you sure you want to delete this overtime request?", "manage-ot-overtime-reason-1": "As per project requirement", "manage-ot-overtime-reason-2": "As per urgent work requirement", "manage-ot-compensate-immediate": "I will take leave immediately after the project ends", "manage-ot-compensate-annual": "Take leave to add to annual leave", "manage-ot-request-error": "Overtime request error", "manage-ot-compensate-hours": "Total Compensate Hours", "manage-ot-reject-request": "Reject overtime request", "manage-ot-from-date": "Overtime from date", "manage-ot-to-date": "Overtime to date", "manage-ot-not-exist": "Overtime request does not exist", "manage-ot-create-success": "Create success", "manage-ot-approve-error": "Overtime approve error", "manage-ot-edit-request": "Edit overtime request", "manage-ot-approve-request": "Approve overtime request", "manage-ot-approve-confirm": "Are you sure you want to approve this overtime report?", "manage-ot-confirm-info": "Confirm information", "manage-ot-status-approved": "Approved", "manage-ot-status-awaiting": "Awaiting approve", "manage-ot-status-awaiting-direct": "Pending Approval – Line Manager", "manage-ot-status-awaiting-next": "Pending Approval – Department Head", "manage-ot-status-awaiting-hr": "Pending Approval – HR", "manage-ot-status-declined": "Declined", "manage-ot-status": "Status", "manage-ot-save": "Save", "manage-ot-submit": "Submit", "manage-ot-cancel": "Cancel", "manage-ot-add": "Add", "manage-ot-close": "Close", "manage-ot-approve-success": "Approve success", "manage-ot-decline-success": "Decline success", "manage-ot-update-success": "Update success", "manage-ot-delete-success": "Delete success", "manage-ot-error": "Error", "manage-ot-fullname": "Full Name", "manage-ot-member-code": "Member Code", "manage-ot-filters": "Filters", "manage-ot-full-name": "Full Name", "manage-ot-department-overtime": "Department", "manage-ot-direct-manager-overtime": "Direct manager", "manage-ot-next-manager-overtime": "Next manager", "manage-ot-overtime-reason": "Overtime reason", "manage-ot-overtime-detail": "Detail", "manage-ot-overtime-other-reason": "Other reason", "manage-ot-overtime-from": "Overtime from", "manage-ot-overtime-to": "Overtime to", "manage-ot-overtime-hours": "Overtime hours", "manage-ot-overtime-total-hours": "Total hours", "manage-ot-overtime-total-compensate-hours": "Total compensate hours", "manage-ot-overtime-equivalent-days": "Equivalent days", "manage-ot-overtime-compensate-type": "Compensatory leave", "manage-ot-overtime-compensate-from": "Compensatory from", "manage-ot-overtime-compensate-to": "Compensatory to", "manage-ot-overtime-compensate-immediate": "I will take leave immediately after the project ends", "manage-ot-overtime-compensate-annual": "Take leave to add to annual leave", "manage-ot-overtime-compensate-hours": "Total Compensate Hours", "manage-ot-overtime-compensate-1": "I will take leave immediately after the project ends", "manage-ot-overtime-compensate-2": "Take leave to add to annual leave", "manage-ot-declines": "Decline", "manage-ot-approve": "Approve", "manage-ot-send-request": "Send", "manage-ot-overtime-modal-title": "Add overtime report", "manage-ot-approve-overtime-request": "Approve overtime request", "manage-ot-edit-overtime-request": "Edit overtime request", "manage-ot-you-are-not-hr": "You are not HR", "manage-ot-delete-ot-request": "Delete OT request", "manage-ot-delete": "Delete", "========================PAYMENT STATUS=======================": "PAYMENT STATUS", "payment-status": "Payment status of project", "payment-status-1": "Payment status", "payment-status-pending-acceptance": "Being accepted", "payment-status-accepted": "Paid ", "payment-status-unprocessed": "Pending acceptance", "payment-status-client-name": "Client Name", "payment-status-expected-invoice-issue-date": "Expected invoice issue date", "payment-status-current-payment-value": "Current payment value", "payment-status-expected-payment-receipt-date": "Expected payment receipt date", "rp-payment-status": "Payment status of project", "rp-payment-status-view": "View - Payment status of project", "rp-payment-status-edit": "Edit - Payment status of project", "rp-payment-status-add": "Add - Payment status of project", "payment-status-no": "No", "payment-status-update-date": "Update Date", "payment-status-project-name": "Project Name", "payment-status-status": "Status", "payment-status-actions": "Actions", "payment-status-search": "Search", "payment-status-edit": "Edit", "manage-ot-reason-project": "As per project requirement", "manage-ot-reason-urgent": "As per urgent work requirement", "manage-leaves-dept.": "Dept.", "manage-leaves-sequence-error": "From date must be after To date the previous leave"}