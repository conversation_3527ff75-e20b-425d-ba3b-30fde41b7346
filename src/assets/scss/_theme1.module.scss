// paper & background
$paper: #ffffff;
$backgroundLight: #f0f5f7;
$backgroundSecondary: #f5f7fc;

// primary - Superio Blue
$primaryLight: #eff4fc;
$primaryMain: #1967d2;
$primaryDark: #0146a6;
$primary200: #4785ff;
$primary800: #003d82;

// secondary - Supporting colors
$secondaryLight: #f7f3d7;
$secondaryMain: #f9ab00;
$secondaryDark: #e9a000;
$secondary200: #ffcc02;
$secondary800: #cc8800;

// success Colors
$successLight: #def2d7;
$success200: #81c784;
$successMain: #34a853;
$successDark: #2e7d32;

// error/danger
$errorLight: #ecc8c5;
$errorMain: #ab3331;
$errorDark: #8b2635;

// warning
$warningLight: #f7f3d7;
$warningMain: #927238;
$warningDark: #6d5a2b;

// info
$infoLight: #cde9f6;
$infoMain: #4780aa;
$infoDark: #2e5984;

// grey scale - Based on Figma text colors
$grey50: #fafafa;
$grey100: #f5f5f5;
$grey200: #ecedf2;
$grey300: #e7e7ec;
$grey400: #bfc8cb;
$grey500: #696969;
$grey600: #2f2d51;
$grey700: #202124;
$grey800: #1a1a1a;
$grey900: #121212;

// Special colors
$orangeLight: #ffe4cc;
$orangeMain: #ff9500;
$orangeDark: #cc7700;

// Additional  specific colors
$borderColor: #ecedf2;
$shadowColor: rgba(64, 79, 104, 0.05);
$overlayColor: rgba(0, 0, 0, 0.1);

// Range slider specific
$rangeTrack: #d4e1f6;
$rangeThumb: #1967d2;

// ==============================|| DARK THEME VARIANTS ||============================== //

// dark theme backgrounds
$darkBackground: #0f1419;
$darkPaper: #1a1f29;
$darkLevel1: #242b38;
$darkLevel2: #2a3441;

// dark primary
$darkPrimaryLight: #4785ff;
$darkPrimaryMain: #4785ff;
$darkPrimaryDark: #1967d2;
$darkPrimary200: #90caf9;
$darkPrimary800: #1565c0;

// dark secondary
$darkSecondaryLight: #ffe082;
$darkSecondaryMain: #ffcc02;
$darkSecondaryDark: #f9ab00;
$darkSecondary200: #ffe082;
$darkSecondary800: #ff8f00;

// dark text variants
$darkTextTitle: #ffffff;
$darkTextPrimary: #e5e7eb;
$darkTextSecondary: #9ca3af;

// ==============================|| JAVASCRIPT EXPORT ||============================== //

:export {
    // paper & background
    paper: $paper;
    backgroundLight: $backgroundLight;
    backgroundSecondary: $backgroundSecondary;

    // primary
    primaryLight: $primaryLight;
    primary200: $primary200;
    primaryMain: $primaryMain;
    primaryDark: $primaryDark;
    primary800: $primary800;

    // secondary
    secondaryLight: $secondaryLight;
    secondary200: $secondary200;
    secondaryMain: $secondaryMain;
    secondaryDark: $secondaryDark;
    secondary800: $secondary800;

    // success
    successLight: $successLight;
    success200: $success200;
    successMain: $successMain;
    successDark: $successDark;

    // error
    errorLight: $errorLight;
    errorMain: $errorMain;
    errorDark: $errorDark;

    // warning
    warningLight: $warningLight;
    warningMain: $warningMain;
    warningDark: $warningDark;

    // info
    infoLight: $infoLight;
    infoMain: $infoMain;
    infoDark: $infoDark;

    // orange
    orangeLight: $orangeLight;
    orangeMain: $orangeMain;
    orangeDark: $orangeDark;

    // grey
    grey50: $grey50;
    grey100: $grey100;
    grey200: $grey200;
    grey300: $grey300;
    grey400: $grey400;
    grey500: $grey500;
    grey600: $grey600;
    grey700: $grey700;
    grey800: $grey800;
    grey900: $grey900;

    // special colors
    borderColor: $borderColor;
    shadowColor: $shadowColor;
    overlayColor: $overlayColor;
    rangeTrack: $rangeTrack;
    rangeThumb: $rangeThumb;

    // ==============================|| DARK THEME VARIANTS ||============================== //

    // paper & background
    darkPaper: $darkPaper;
    darkBackground: $darkBackground;
    darkLevel1: $darkLevel1;
    darkLevel2: $darkLevel2;

    // text variants
    darkTextTitle: $darkTextTitle;
    darkTextPrimary: $darkTextPrimary;
    darkTextSecondary: $darkTextSecondary;

    // primary dark
    darkPrimaryLight: $darkPrimaryLight;
    darkPrimaryMain: $darkPrimaryMain;
    darkPrimaryDark: $darkPrimaryDark;
    darkPrimary200: $darkPrimary200;
    darkPrimary800: $darkPrimary800;

    // secondary dark
    darkSecondaryLight: $darkSecondaryLight;
    darkSecondaryMain: $darkSecondaryMain;
    darkSecondaryDark: $darkSecondaryDark;
    darkSecondary200: $darkSecondary200;
    darkSecondary800: $darkSecondary800;
}
