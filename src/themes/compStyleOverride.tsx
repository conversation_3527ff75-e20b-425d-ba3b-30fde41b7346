// project imports
import { Theme } from '@mui/material/styles';

export default function componentStyleOverrides(theme: Theme, borderRadius: number, outlinedFilled: boolean) {
    const mode = theme.palette.mode;
    const bgColor = mode === 'dark' ? theme.palette.dark[800] : theme.palette.grey[50];
    const menuSelectedBack = mode === 'dark' ? theme.palette.secondary.main + 15 : theme.palette.secondary.light;
    const menuSelected = mode === 'dark' ? theme.palette.secondary.main : theme.palette.secondary.dark;

    return {
        MuiTypography: {
            styleOverrides: {
                root: {
                    fontSize: '0.75rem'
                },
                h3: {
                    fontSize: '1rem !important'
                },
                h2: {
                    fontSize: '1.5rem !important'
                }
            }
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    fontWeight: 500,
                    borderRadius: '8px',
                    fontSize: '0.9375rem',
                    lineHeight: 1.44,
                    textTransform: 'none',
                    padding: '17px 50px',
                    boxShadow: 'none',
                    border: 'none',
                    '&:hover': {
                        boxShadow: 'none'
                    }
                },
                // Primary Button - Blue
                containedPrimary: {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.background.paper,
                    '&:hover': {
                        backgroundColor: theme.palette.primary.dark
                    }
                },
                // Secondary Button - Amber
                containedSecondary: {
                    backgroundColor: theme.palette.secondary.main,
                    color: theme.palette.background.paper,
                    '&:hover': {
                        backgroundColor: theme.palette.secondary.dark
                    }
                },
                // Third Button - Light Blue Background
                outlinedPrimary: {
                    backgroundColor: theme.palette.primary.light,
                    borderColor: 'transparent',
                    color: theme.palette.primary.main,
                    '&:hover': {
                        backgroundColor: theme.palette.primary.main,
                        color: theme.palette.background.paper,
                        borderColor: 'transparent'
                    }
                },
                // Fourth Button - Light Green Background
                outlinedSecondary: {
                    backgroundColor: theme.palette.success.light,
                    borderColor: 'transparent',
                    color: theme.palette.success.main,
                    '&:hover': {
                        backgroundColor: theme.palette.success.main,
                        color: theme.palette.background.paper,
                        borderColor: 'transparent'
                    }
                }
            }
        },
        MuiPaper: {
            defaultProps: {
                elevation: 0
            },
            styleOverrides: {
                root: {
                    backgroundImage: 'none',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    border: `1px solid ${theme.palette.divider}`
                },
                rounded: {
                    borderRadius: '8px'
                },
                elevation0: {
                    boxShadow: 'none'
                },
                elevation1: {
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)'
                }
            }
        },
        MuiCardHeader: {
            styleOverrides: {
                root: {
                    color: theme.palette.text.dark,
                    padding: '24px'
                },
                title: {
                    fontSize: '1.125rem'
                }
            }
        },
        MuiCardContent: {
            styleOverrides: {
                root: {
                    padding: '24px'
                }
            }
        },
        MuiCardActions: {
            styleOverrides: {
                root: {
                    padding: '24px'
                }
            }
        },
        MuiAlert: {
            styleOverrides: {
                root: {
                    alignItems: 'center',
                    fontSize: '0.9375rem',
                    borderRadius: '4px',
                    border: 'none',
                    boxShadow: 'none',
                    minHeight: '75px',
                    padding: '18px 24px',
                    // Info Message Box - Blue theme
                    '&.MuiAlert-standardInfo': {
                        backgroundColor: '#CDE9F6',
                        color: '#4780AA',
                        '& .MuiAlert-icon': {
                            color: '#4780AA'
                        }
                    },
                    // Warning Message Box - Yellow theme
                    '&.MuiAlert-standardWarning': {
                        backgroundColor: '#F7F3D7',
                        color: '#927238',
                        '& .MuiAlert-icon': {
                            color: '#927238'
                        }
                    },
                    // Error Message Box - Red theme
                    '&.MuiAlert-standardError': {
                        backgroundColor: '#ECC8C5',
                        color: '#AB3331',
                        '& .MuiAlert-icon': {
                            color: '#AB3331'
                        }
                    },
                    // Success Message Box - Green theme
                    '&.MuiAlert-standardSuccess': {
                        backgroundColor: '#DEF2D7',
                        color: '#5B7052',
                        '& .MuiAlert-icon': {
                            color: '#5B7052'
                        }
                    }
                },
                message: {
                    fontWeight: 400,
                    lineHeight: 1.44,
                    display: 'flex',
                    alignItems: 'center'
                },
                icon: {
                    marginRight: '12px',
                    fontSize: '1.25rem'
                }
            }
        },
        MuiListItemButton: {
            styleOverrides: {
                root: {
                    color: theme.palette.text.primary,
                    paddingTop: '10px',
                    paddingBottom: '10px',
                    '&.Mui-selected': {
                        color: menuSelected,
                        backgroundColor: menuSelectedBack,
                        '&:hover': {
                            backgroundColor: menuSelectedBack
                        },
                        '& .MuiListItemIcon-root': {
                            color: menuSelected
                        }
                    },
                    '&:hover': {
                        backgroundColor: menuSelectedBack,
                        color: menuSelected,
                        '& .MuiListItemIcon-root': {
                            color: menuSelected
                        }
                    }
                }
            }
        },
        MuiListItemIcon: {
            styleOverrides: {
                root: {
                    color: theme.palette.text.primary,
                    minWidth: '36px'
                }
            }
        },
        MuiListItemText: {
            styleOverrides: {
                primary: {
                    color: theme.palette.text.dark
                }
            }
        },
        MuiInputBase: {
            styleOverrides: {
                input: {
                    color: theme.palette.text.secondary,
                    '&::placeholder': {
                        color: theme.palette.text.secondary,
                        fontSize: '0.9375rem'
                    }
                }
            }
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    background: theme.palette.background.paper,
                    borderRadius: '8px',
                    '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.divider
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                        borderWidth: '1px'
                    },
                    '&.MuiInputBase-multiline': {
                        padding: 1
                    }
                },
                input: {
                    fontWeight: 400,
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    background: 'transparent',
                    padding: '15.5px 14px',
                    borderRadius: '8px',
                    '&::placeholder': {
                        color: theme.palette.text.secondary,
                        opacity: 1
                    },
                    '&.MuiInputBase-inputSizeSmall': {
                        padding: '10px 14px',
                        '&.MuiInputBase-inputAdornedStart': {
                            paddingLeft: 0
                        }
                    },
                    '&.Mui-disabled': {
                        background: theme.palette.grey[50]
                    }
                },
                inputAdornedStart: {
                    paddingLeft: 4
                },
                notchedOutline: {
                    borderRadius: '8px'
                }
            }
        },
        MuiSlider: {
            styleOverrides: {
                root: {
                    height: '5px',
                    '&.Mui-disabled': {
                        color: '#D4E1F6'
                    }
                },
                rail: {
                    backgroundColor: '#D4E1F6',
                    height: '5px',
                    borderRadius: '8px',
                    opacity: 1
                },
                track: {
                    backgroundColor: theme.palette.primary.main,
                    height: '5px',
                    borderRadius: '8px',
                    border: 'none'
                },
                thumb: {
                    backgroundColor: theme.palette.background.paper,
                    border: `2px solid ${theme.palette.primary.main}`,
                    width: '17px',
                    height: '17px',
                    '&:hover': {
                        boxShadow: '0px 0px 0px 8px rgba(25, 103, 210, 0.16)'
                    },
                    '&.Mui-focusVisible': {
                        boxShadow: '0px 0px 0px 8px rgba(25, 103, 210, 0.16)'
                    },
                    '&.Mui-active': {
                        boxShadow: '0px 0px 0px 8px rgba(25, 103, 210, 0.16)'
                    }
                },
                mark: {
                    backgroundColor: '#D4E1F6',
                    width: '4px',
                    height: '4px',
                    borderRadius: '50%'
                },
                valueLabel: {
                    backgroundColor: theme.palette.grey[50],
                    color: theme.palette.primary.main,
                    fontSize: '0.875rem',
                    fontWeight: 400,
                    borderRadius: '8px',
                    padding: '6px 12px',
                    '&:before': {
                        display: 'none'
                    }
                }
            }
        },
        MuiAutocomplete: {
            styleOverrides: {
                root: {
                    '& .MuiAutocomplete-tag': {
                        backgroundColor: theme.palette.grey[50],
                        borderRadius: '8px',
                        color: theme.palette.text.primary,
                        fontSize: '0.875rem',
                        fontWeight: 400,
                        margin: '2px',
                        height: '24px',
                        '.MuiChip-deleteIcon': {
                            color: theme.palette.text.secondary,
                            fontSize: '16px',
                            '&:hover': {
                                color: theme.palette.primary.main
                            }
                        }
                    },
                    '& .MuiAutocomplete-inputRoot': {
                        padding: '6px 12px !important'
                    }
                },
                popper: {
                    borderRadius: '8px',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    border: `1px solid ${theme.palette.divider}`,
                    backgroundColor: theme.palette.background.paper,
                    '& .MuiAutocomplete-paper': {
                        borderRadius: '8px',
                        boxShadow: 'none',
                        backgroundColor: theme.palette.background.paper
                    }
                },
                listbox: {
                    fontSize: '0.9375rem',
                    padding: '4px 0'
                },
                option: {
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    padding: '8px 16px',
                    '&:hover': {
                        backgroundColor: theme.palette.grey[50]
                    },
                    '&.Mui-focused': {
                        backgroundColor: theme.palette.grey[50]
                    },
                    '&[aria-selected="true"]': {
                        backgroundColor: theme.palette.primary.light,
                        color: theme.palette.primary.main
                    }
                }
            }
        },
        MuiDivider: {
            styleOverrides: {
                root: {
                    borderColor: theme.palette.divider,
                    opacity: mode === 'dark' ? 0.2 : 1
                }
            }
        },
        MuiSelect: {
            styleOverrides: {
                select: {
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    backgroundColor: theme.palette.background.paper,
                    borderRadius: '8px',
                    padding: '15.5px 14px',
                    '&:focus': {
                        backgroundColor: 'transparent'
                    }
                },
                icon: {
                    color: theme.palette.text.secondary,
                    fontSize: '1.25rem',
                    '&.Mui-disabled': {
                        color: theme.palette.divider
                    }
                }
            }
        },
        MuiMenuItem: {
            styleOverrides: {
                root: {
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    padding: '8px 16px',
                    '&:hover': {
                        backgroundColor: theme.palette.grey[50]
                    },
                    '&.Mui-selected': {
                        backgroundColor: theme.palette.primary.light,
                        color: theme.palette.primary.main,
                        '&:hover': {
                            backgroundColor: theme.palette.primary.light
                        }
                    }
                }
            }
        },
        MuiCheckbox: {
            styleOverrides: {
                root: {
                    color: theme.palette.divider,
                    borderRadius: '4px',
                    padding: '8px',
                    '&.Mui-checked': {
                        color: theme.palette.primary.main,
                        '&:hover': {
                            backgroundColor: 'rgba(25, 103, 210, 0.04)'
                        }
                    },
                    '&:hover': {
                        backgroundColor: 'rgba(25, 103, 210, 0.04)'
                    },
                    '&.Mui-disabled': {
                        color: theme.palette.divider
                    }
                }
            }
        },
        MuiRadio: {
            styleOverrides: {
                root: {
                    color: theme.palette.divider,
                    padding: '8px',
                    '&.Mui-checked': {
                        color: theme.palette.primary.main,
                        '&:hover': {
                            backgroundColor: 'rgba(25, 103, 210, 0.04)'
                        }
                    },
                    '&:hover': {
                        backgroundColor: 'rgba(25, 103, 210, 0.04)'
                    },
                    '&.Mui-disabled': {
                        color: theme.palette.divider
                    }
                }
            }
        },
        MuiSwitch: {
            styleOverrides: {
                root: {
                    width: 60,
                    height: 30,
                    padding: 0,
                    margin: 8,
                    '& .MuiSwitch-switchBase': {
                        padding: 0,
                        margin: 2,
                        transitionDuration: '300ms',
                        '&.Mui-checked': {
                            transform: 'translateX(30px)',
                            color: theme.palette.background.paper,
                            '& + .MuiSwitch-track': {
                                backgroundColor: theme.palette.primary.main,
                                opacity: 1,
                                border: 0
                            },
                            '&.Mui-disabled + .MuiSwitch-track': {
                                opacity: 0.5
                            }
                        },
                        '&.Mui-focusVisible .MuiSwitch-thumb': {
                            color: theme.palette.primary.main,
                            border: `6px solid ${theme.palette.background.paper}`
                        },
                        '&.Mui-disabled .MuiSwitch-thumb': {
                            color: theme.palette.divider
                        },
                        '&.Mui-disabled + .MuiSwitch-track': {
                            opacity: 0.7
                        }
                    },
                    '& .MuiSwitch-thumb': {
                        backgroundColor: theme.palette.background.paper,
                        boxSizing: 'border-box',
                        width: 26,
                        height: 26,
                        border: `2px solid ${theme.palette.divider}`,
                        '&:before': {
                            content: "''",
                            position: 'absolute',
                            width: '100%',
                            height: '100%',
                            left: 0,
                            top: 0,
                            backgroundRepeat: 'no-repeat',
                            backgroundPosition: 'center'
                        }
                    },
                    '& .MuiSwitch-track': {
                        borderRadius: 30 / 2,
                        backgroundColor: theme.palette.divider,
                        opacity: 1,
                        transition: 'background-color 500ms'
                    }
                }
            }
        },
        MuiLinearProgress: {
            styleOverrides: {
                root: {
                    height: 10,
                    borderRadius: 10,
                    backgroundColor: '#D4E1F6',
                    '&.MuiLinearProgress-colorPrimary': {
                        backgroundColor: '#D4E1F6'
                    },
                    '&.MuiLinearProgress-colorSecondary': {
                        backgroundColor: '#D4E1F6'
                    }
                },
                bar: {
                    borderRadius: 10,
                    backgroundColor: theme.palette.primary.main,
                    '&.MuiLinearProgress-barColorPrimary': {
                        backgroundColor: theme.palette.primary.main
                    },
                    '&.MuiLinearProgress-barColorSecondary': {
                        backgroundColor: theme.palette.success.main
                    }
                }
            }
        },
        MuiAccordion: {
            styleOverrides: {
                root: {
                    backgroundColor: theme.palette.background.paper,
                    borderRadius: '8px',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    border: `1px solid ${theme.palette.divider}`,
                    margin: '0px 0px 16px 0px',
                    '&:before': {
                        display: 'none'
                    },
                    '&.Mui-expanded': {
                        margin: '0px 0px 16px 0px',
                        boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)'
                    }
                }
            }
        },
        MuiAccordionSummary: {
            styleOverrides: {
                root: {
                    fontSize: '1rem',
                    fontWeight: 500,
                    color: theme.palette.text.primary,
                    padding: '16px 24px',
                    minHeight: '70px',
                    '&.Mui-expanded': {
                        minHeight: '70px'
                    }
                },
                content: {
                    margin: '12px 0px',
                    '&.Mui-expanded': {
                        margin: '12px 0px'
                    }
                },
                expandIconWrapper: {
                    color: '#2F2D51',
                    '&.Mui-expanded': {
                        transform: 'rotate(180deg)'
                    }
                }
            }
        },
        MuiAccordionDetails: {
            styleOverrides: {
                root: {
                    fontSize: '0.9375rem',
                    fontWeight: 400,
                    color: theme.palette.text.secondary,
                    padding: '0px 24px 24px 24px',
                    lineHeight: 1.6,
                    borderTop: '1px solid #E7E7EC'
                }
            }
        },
        MuiFormControlLabel: {
            styleOverrides: {
                root: {
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    marginLeft: '0px',
                    marginRight: '16px',
                    '&.Mui-disabled': {
                        color: theme.palette.divider
                    }
                },
                label: {
                    fontSize: '0.9375rem',
                    color: theme.palette.text.secondary,
                    '&.Mui-disabled': {
                        color: theme.palette.divider
                    }
                }
            }
        },
        MuiAvatar: {
            styleOverrides: {
                root: {
                    color: mode === 'dark' ? theme.palette.dark.main : theme.palette.primary.dark,
                    background: mode === 'dark' ? theme.palette.text.primary : theme.palette.primary[200]
                }
            }
        },
        MuiChip: {
            styleOverrides: {
                root: {
                    '&.MuiChip-deletable .MuiChip-deleteIcon': {
                        color: 'inherit'
                    }
                }
            }
        },
        MuiTimelineContent: {
            styleOverrides: {
                root: {
                    color: theme.palette.text.dark,
                    fontSize: '16px'
                }
            }
        },
        MuiTreeItem: {
            styleOverrides: {
                label: {
                    marginTop: 14,
                    marginBottom: 14
                }
            }
        },
        MuiTimelineDot: {
            styleOverrides: {
                root: {
                    boxShadow: 'none'
                }
            }
        },
        MuiTabs: {
            styleOverrides: {
                root: {
                    backgroundColor: theme.palette.grey[50],
                    borderRadius: '8px',
                    padding: '4px',
                    minHeight: 'auto',
                    '& .MuiTabs-indicator': {
                        display: 'none'
                    }
                },
                flexContainer: {
                    gap: '4px',
                    borderBottom: 'none'
                }
            }
        },
        MuiTab: {
            styleOverrides: {
                root: {
                    fontSize: '0.9375rem',
                    fontWeight: 400,
                    lineHeight: 1.44,
                    textTransform: 'none',
                    color: theme.palette.text.secondary,
                    backgroundColor: 'transparent',
                    borderRadius: '8px',
                    padding: '7px 20px 4px',
                    minHeight: 'auto',
                    minWidth: 'auto',
                    '&.Mui-selected': {
                        backgroundColor: theme.palette.background.paper,
                        color: theme.palette.text.primary,
                        fontWeight: 400
                    },
                    '&:hover': {
                        backgroundColor: theme.palette.background.paper,
                        color: theme.palette.text.primary
                    }
                }
            }
        },
        MuiInternalDateTimePickerTabs: {
            styleOverrides: {
                tabs: {
                    backgroundColor: theme.palette.grey[50],
                    borderRadius: '8px',
                    padding: '4px',
                    '& .MuiTabs-flexContainer': {
                        gap: '4px'
                    },
                    '& .MuiTab-root': {
                        fontSize: '0.9375rem',
                        color: theme.palette.text.secondary,
                        backgroundColor: 'transparent',
                        borderRadius: '8px',
                        padding: '7px 20px 4px',
                        '&.Mui-selected': {
                            backgroundColor: theme.palette.background.paper,
                            color: theme.palette.text.primary
                        }
                    },
                    '& .MuiTabs-indicator': {
                        display: 'none'
                    }
                }
            }
        },
        MuiDialog: {
            styleOverrides: {
                paper: {
                    padding: '12px 0 12px 0'
                }
            }
        },
        MuiTableCell: {
            styleOverrides: {
                root: {
                    borderColor: theme.palette.divider,
                    borderWidth: '1px',
                    padding: '16px 24px',
                    fontSize: '0.9375rem',
                    lineHeight: 1.44,
                    color: theme.palette.text.secondary,
                    '&.MuiTableCell-head': {
                        backgroundColor: '#F5F7FC',
                        color: theme.palette.primary.main,
                        fontSize: '1rem',
                        fontWeight: 500,
                        borderBottom: `1px solid ${theme.palette.divider}`
                    },
                    '&.MuiTableCell-body': {
                        backgroundColor: theme.palette.background.paper,
                        fontWeight: 400,
                        borderBottom: `1px solid ${theme.palette.divider}`
                    }
                }
            }
        },
        MuiTableHead: {
            styleOverrides: {
                root: {
                    backgroundColor: '#F5F7FC',
                    '& .MuiTableCell-root': {
                        backgroundColor: '#F5F7FC'
                    },
                    '& .MuiTableRow-root:first-of-type .MuiTableCell-root:first-of-type': {
                        borderTopLeftRadius: '8px'
                    },
                    '& .MuiTableRow-root:first-of-type .MuiTableCell-root:last-of-type': {
                        borderTopRightRadius: '8px'
                    }
                }
            }
        },
        MuiTableContainer: {
            styleOverrides: {
                root: {
                    borderRadius: '8px',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    border: `1px solid ${theme.palette.divider}`,
                    backgroundColor: theme.palette.background.paper
                }
            }
        },
        MuiTooltip: {
            styleOverrides: {
                tooltip: {
                    fontSize: '0.875rem',
                    fontWeight: 400,
                    lineHeight: 1.44,
                    color: theme.palette.text.primary,
                    backgroundColor: theme.palette.grey[50],
                    borderRadius: '8px',
                    padding: '8px 12px',
                    boxShadow: '0px 6px 15px 0px rgba(64, 79, 104, 0.05)',
                    border: `1px solid ${theme.palette.divider}`,
                    maxWidth: '300px'
                },
                arrow: {
                    color: theme.palette.grey[50],
                    '&:before': {
                        backgroundColor: theme.palette.grey[50],
                        border: `1px solid ${theme.palette.divider}`
                    }
                },
                popper: {
                    '&[data-popper-placement*="bottom"] .MuiTooltip-arrow': {
                        '&:before': {
                            borderTop: `1px solid ${theme.palette.divider}`,
                            borderLeft: `1px solid ${theme.palette.divider}`
                        }
                    },
                    '&[data-popper-placement*="top"] .MuiTooltip-arrow': {
                        '&:before': {
                            borderBottom: `1px solid ${theme.palette.divider}`,
                            borderRight: `1px solid ${theme.palette.divider}`
                        }
                    },
                    '&[data-popper-placement*="right"] .MuiTooltip-arrow': {
                        '&:before': {
                            borderTop: `1px solid ${theme.palette.divider}`,
                            borderLeft: `1px solid ${theme.palette.divider}`
                        }
                    },
                    '&[data-popper-placement*="left"] .MuiTooltip-arrow': {
                        '&:before': {
                            borderBottom: `1px solid ${theme.palette.divider}`,
                            borderRight: `1px solid ${theme.palette.divider}`
                        }
                    }
                }
            }
        },
        MuiBreadcrumbs: {
            styleOverrides: {
                root: {
                    fontSize: '0.9375rem',
                    fontWeight: 400,
                    color: theme.palette.text.secondary
                },
                separator: {
                    color: theme.palette.text.secondary,
                    margin: '0px 8px'
                },
                li: {
                    fontSize: '0.9375rem',
                    '& a': {
                        color: theme.palette.text.secondary,
                        textDecoration: 'none',
                        '&:hover': {
                            color: theme.palette.primary.main,
                            textDecoration: 'underline'
                        }
                    },
                    '& span': {
                        color: theme.palette.text.primary,
                        fontWeight: 400
                    }
                }
            }
        },
        MuiDialogTitle: {
            styleOverrides: {
                root: {
                    fontSize: '1rem'
                }
            }
        },
        MuiFormHelperText: {
            styleOverrides: {
                root: {
                    marginLeft: 0
                }
            }
        }
    };
}
