import { lazy } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

// Project imports
import Loadable from 'components/Loadable';
import MainLayout from 'layout/MainLayout';
import AuthLayout from 'layout/AuthLayout';
import DashboardLayout from 'layout/DashboardLayout';

// Guard Routes
import AuthGuard from 'utils/route-guard/AuthGuard';
import GuestGuard from 'utils/route-guard/GuestGuard';
import NotFound from 'utils/route-guard/NotFound';

// Public Routes (accessible to everyone)
const Homepage = Loadable(lazy(() => import('pages/homepage')));
// const Jobs = Loadable(lazy(() => import('pages/Jobs')));
// const Companies = Loadable(lazy(() => import('pages/Companies')));

// Auth Routes (only for non-authenticated users)
const AuthLogin = Loadable(lazy(() => import('pages/Login')));
const AuthRegister = Loadable(lazy(() => import('pages/Register')));
const AuthForgotPassword = Loadable(lazy(() => import('pages/ForgotPassword')));

// Private Routes (only for authenticated users)
const Dashboard = Loadable(lazy(() => import('pages/Dashboard')));

// ==============================|| MAIN ROUTES RENDER ||============================== //

const MainRoutes = () => (
    <Routes>
        {/** Public Routes - Accessible to everyone */}
        <Route path="/" element={<MainLayout />}>
            <Route index element={<Homepage />} />
        </Route>

        {/** Authentication Routes - Only for non-authenticated users */}
        <Route element={<GuestGuard />}>
            <Route element={<AuthLayout />}>
                <Route path="/login" element={<AuthLogin />} />
                <Route path="/register" element={<AuthRegister />} />
                <Route path="/forgot-password" element={<AuthForgotPassword />} />
            </Route>
        </Route>

        {/** Protected Routes - Only for authenticated users */}
        <Route element={<AuthGuard />}>
            <Route element={<DashboardLayout />}>
                <Route path="/dashboard" element={<Dashboard />} />

                {/* Candidate Routes */}
                <Route path="/candidate/dashboard" element={<div>Candidate Dashboard</div>} />
                <Route path="/candidate/profile" element={<div>Candidate Profile</div>} />
                <Route path="/candidate/applications" element={<div>My Applications</div>} />
                <Route path="/candidate/bookmarks" element={<div>My Bookmarks</div>} />
                <Route path="/candidate/job-alerts" element={<div>Job Alerts</div>} />

                {/* Employer Routes */}
                <Route path="/employer/dashboard" element={<div>Employer Dashboard</div>} />
                <Route path="/employer/profile" element={<div>Company Profile</div>} />
                <Route path="/employer/jobs" element={<div>Manage Jobs</div>} />
                <Route path="/employer/candidates" element={<div>Browse Candidates</div>} />
                <Route path="/employer/packages" element={<div>Job Packages</div>} />

                {/* Common Protected Routes */}
                <Route path="/post-job" element={<div>Post Job</div>} />
                <Route path="/upload-cv" element={<div>Upload CV</div>} />
                <Route path="/profile" element={<div>Profile</div>} />
                <Route path="/settings" element={<div>Settings</div>} />
            </Route>
        </Route>

        {/** Redirect old routes */}
        <Route path="/home" element={<Navigate to="/" replace />} />

        {/** Page not found route */}
        <Route path="*" element={<NotFound />} />
    </Routes>
);

export default MainRoutes;
