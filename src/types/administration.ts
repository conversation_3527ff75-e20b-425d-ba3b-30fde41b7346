import { ITreeItem } from './index';

// Manage project
export interface IProject {
    id?: {};
    projectId: number;
    projectName: string;
    projectPhase: string;
    deptId: string;
    totalQuota: number;
    projectCostLimit: number;
    typeId?: any;
    startDate: Date;
    endDate: Date;
    type: string;
    sort: number;
    parentId: string;
    isMainProject?: any;
    projectStatus: string;
    creator: string;
    created: Date;
    lastUpdate?: any;
    userUpdate?: any;
    billable: string;
    contractSize: number;
    licenseAmount?: any;
    contractNo: string;
    typeCode: string;
    totalEffort?: any;
    percentageComplete?: any;
    mainProject?: any;
    projectManager: {
        userId: string;
        firstName: string;
        lastName: string;
        userName: string;
    } | null;
    note?: string;
    color?: string;
    client?: string;
    technology?: string;
    desc?: string;
    domain?: string;
    effort?: string;
    effortArise?: string;
}

export interface IProjectList {
    content: IProject[];
}

export interface IProjectHeadCount {
    id: {};
    projectId: string;
    userId: string;
    roleId: string;
    userName: string;
    idHexString: string;
    projectName: string;
    projectType: string;
    fromDate: Date;
    toDate?: any;
    firstName: string;
    lastName: string;
    memberCode?: string;
}

export interface IProjectDetail {
    project: IProject;
    headcount: IProjectHeadCount[];
}

export interface IProjectDetailResponse {
    content: IProjectDetail;
}

export interface IQuotaUpdateHistory {
    id: {};
    projectId: number | null;
    totalQuota: number | null;
    updateDate: string;
    userUpdate: string;
}
export interface IQuotaUpdateHistoryList {
    content: IQuotaUpdateHistory[];
}

// manage special Hours
export interface ISpecialHours {
    idHexString: string;
    userId: string;
    userName: string;
    fromDate: string | null;
    toDate: string | null;
    type: string;
    hourPerDay: string;
    note: string;
    totalHour?: string;
    created?: string;
    creator?: string;
    lastUpdate?: string;
    userUpdate?: string;
    lastName?: string;
    firstName?: string;
    memberCode?: string;
}

// Manage holiday
export interface IHoliday {
    idHexString: string;
    fromDate: Date | null;
    toDate: Date | null;
    type: string;
    note: string;
    dateCreate: string;
    userCreate: string;
    userUpdate: string;
    lastUpdate: string;
}

export interface IHolidayList {
    content: IHoliday[];
}

// Manage config
export interface ISystemConfig {
    idHexString?: string;
    key: string;
    value: string;
    valueTypeDate?: string;
    description: string;
    userCreate?: string;
    userUpdate?: string;
    lastUpdate?: any;
    dateCreate?: string;
    note?: string;
}
export interface ISystemConfigList {
    content: ISystemConfig[];
}

// Manage group
export interface IGroupItem {
    idHexString: string;
    groupId: string;
    groupName: string;
    groupType: string;
    note: string;
    children: ITreeItem[];
}

export interface IGroups {
    content: IGroupItem[];
}

//Email Config
export interface IEmailConfig {
    id?: {};
    idHexString?: string;
    emailCode: string;
    emailType?: string;
    role?: string;
    template: string;
    sendTo: string[];
    sendCC: string[];
    sendBCC: string[];
    content: string;
    userName: string;
    subject: string;
    nameFile: string;
    status?: string;
    timeSendMail: {
        day: string;
        hour: string;
        weekdays: string;
    };
}

export interface IEmailConfigList {
    content: IEmailConfig[];
}

export interface IAssignedUser {
    memberCode: string;
    userName: string;
    firstName: string;
    lastName: string;
    title: string;
}

// cv
export interface ISkill {
    idHexString: string;
    name: string;
    type: string;
    userUpdate: string;
    lastUpdate: string;
}

export interface ITechnology {
    techType: string;
    skillList: ISkill[];
    lastUpdate?: string;
    userUpdate: string;
}

export interface ILanguage {
    idHexString?: string;
    userUpdate?: string;
    name: string;
}

export interface IReference {
    idHexString?: string;
    userUpdate?: string;
    fullName: string;
    organization: string;
    position: string;
    address: string;
    phoneNumber: string;
    email: string;
}

// Exchange Rate config

export interface IExchangeRate {
    idHexString?: string;
    userUpdate?: string;
    exchangeRate?: number;
    currency: string;
    year: number | string | null;
}
export interface ITimeSheetConfig {
    id: string;
    key: string;
    name: string;
    minValue: string | number | null;
    maxValue: string | number | null;
    typeCode: string;
    note: string;
    color: string;
    sort: string;
    lastUpdated: string;
    userUpdated: string;
}
export interface ITimeSheetConfigList {
    content: ITimeSheetConfig[];
}
