import { IGroup } from './master';

export interface IUserOnboardHistory {
    fromDate?: string | null;
    toDate?: string | null;
    officialOnboardDate?: string | null;
    contractor?: boolean | string | null;
}

export interface IUserRankHistory {
    fromDate?: string | null;
    toDate?: string | null;
    rankId?: string | null;
    titleCode?: string | null;
    timesheetEnough: boolean | 'not-enough' | 'enough';
}
export interface IUserBillableHistory {
    fromDate?: string | null;
    toDate?: string | null;
}

export interface IMember {
    id: {};
    userId: string;
    memberCode: string;
    userName: string;
    userType: null | string;
    lastName: string;
    firstName: string;
    fullNameVi?: string;
    fullNameEn?: string;
    timeStatus?: string;
    userDept: string;
    departmentId: string;
    onboardDate: string | null;
    outboardDate: string | null;
    created: string;
    creator: string;
    lastUpdate: string;
    idHexString: string | null;
    userUpdate: string;
    status: string;
    titleCode: string;
    rankId: string;
    rankCost: number;
    allocationCost: number;
    contractor: string | boolean;
    logtime: string | boolean;
    isCustomer: boolean;
    userLevel: string;
    groups: IGroup[];
    userOnboardHistoryList?: IUserOnboardHistory[];
    userRankHistoryList?: IUserRankHistory[];
    userBillableHistoryList?: IUserBillableHistory[];
    projectPermissionEntity?: {
        type: string;
        assignedProjectList?: { projectId: number; projectName: string }[];
    } | null;
    effortArise: string;
}

export interface IMemberList {
    content: IMember[];
}

export interface IMemberResponse {
    content: string;
}

export interface IResetPasswordRequest {
    userId: string;
}
