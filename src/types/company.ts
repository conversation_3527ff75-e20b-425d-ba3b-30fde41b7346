// ================================|| COMPANY DOMAIN TYPES ||================================ //

// Base Company interface
export interface ICompany {
    id: number;
    name: string;
    industry: string;
    location: string;
    size: string;
    description?: string;
    website?: string;
    logo?: string;
    address?: string;
    foundedYear?: number;
    openPositions: number;
    totalEmployees?: number;
    isActive?: boolean;
    createdAt?: string;
    updatedAt?: string;
    rating?: number;
    benefits?: string[];
    culture?: string;
    workingHours?: string;
}

// Extended Company with user interaction states
export interface ICompanyWithInteraction extends ICompany {
    isFollowed?: boolean;
    isFeatured?: boolean;
    isVerified?: boolean;
    viewCount?: number;
}

// Company search and filter types
export interface ICompanySearchFilters {
    keywords?: string;
    location?: string;
    industry?: string;
    companySize?: string;
    foundedYear?: {
        min?: number;
        max?: number;
    };
    hasOpenings?: boolean;
    isVerified?: boolean;
    rating?: number;
}

// Company filter system types (similar to job filters)
export interface ICompanyFilterOption {
    value: string;
    label: string;
}

export interface ICompanyFilterType {
    id: string;
    label: string;
    options: ICompanyFilterOption[];
}

export interface ICompanyDynamicFilter {
    filterType: string;
    filterValue: string;
}

// Company component props
export interface ICompanyItemProps {
    company: ICompanyWithInteraction;
    variant?: 'default' | 'compact' | 'featured' | 'list';
    showFollow?: boolean;
    showDetails?: boolean;
    onCompanyClick?: (company: ICompanyWithInteraction) => void;
    onFollowClick?: (company: ICompanyWithInteraction, event: React.MouseEvent) => void;
    sx?: object;
}

export interface ICompanyListProps {
    companies: ICompanyWithInteraction[];
    loading?: boolean;
    variant?: 'grid' | 'list';
    itemVariant?: 'default' | 'compact' | 'featured';
    showLoadMore?: boolean;
    showFilters?: boolean;
    onLoadMore?: () => void;
    onCompanyClick?: (company: ICompanyWithInteraction) => void;
    onFollowClick?: (company: ICompanyWithInteraction, event: React.MouseEvent) => void;
    onFiltersChange?: (filters: ICompanySearchFilters) => void;
}

// Company detail types
export interface ICompanyDetail extends ICompanyWithInteraction {
    jobs?: Array<{
        id: number;
        title: string;
        location: string;
        salary: string;
        type: string;
        postedTime: string;
    }>;
    team?: Array<{
        id: number;
        name: string;
        role: string;
        avatar?: string;
    }>;
    gallery?: string[];
    socialLinks?: {
        linkedin?: string;
        facebook?: string;
        twitter?: string;
        instagram?: string;
    };
}

// Company statistics
export interface ICompanyStats {
    totalCompanies: number;
    totalOpenings: number;
    averageRating: number;
    topIndustries: Array<{
        industry: string;
        count: number;
    }>;
}
