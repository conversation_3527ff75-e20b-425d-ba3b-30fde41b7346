import { IOption } from './common';

export interface INonBillablesConfig {
    id: string;
    name: string;
    key: string;
    value: number | null;
    typeCode: string | IOption;
    note: string;
    lastUpdated: string;
    userUpdated: string;
    minValue: number | null;
    maxValue: number | null;
    color: string | null;
}

export interface INonBillablesConfigResponse {
    content: INonBillablesConfig[];
}
export interface INonBillablesConfigRequest {
    name: string;
    key: string;
    value: number;
    typeCode: string | IOption;
    note: string;
    userUpdated: string;
}
