import { IOption } from './common';

// Monthly effort summary
export interface IProjectTypeByDepartment {
    department: string;
    projectType: {
        type: string;
        total: number;
        color: string | null;
    }[];
}

export interface IEffortPlan {
    id: string;
    department: string;
    sentReport: number;
    notSentReport: number;
    show?: boolean;
}

export interface ILogTimesheetOnRedmine {
    logTime: number[];
}

export interface IEffortByProjectType {
    type: string;
    effort: number;
    color: string;
}

export interface IMonthEffortSummaryInfo {
    effortByProjectType: IEffortByProjectType[];
    effortPlan: IEffortPlan[];
    logTimesheetOnRedmine: ILogTimesheetOnRedmine;
    projectTypeByDepartment: IProjectTypeByDepartment[];
}

export interface IMonthlyEffortSummaryResponse {
    content: IMonthEffortSummaryInfo;
}

// Monthly effort project
export interface IMonthlyEffortProjectUserProject {
    id: {
        timestamp: number;
        date: string;
    };
    projectId: string;
    projectName: string;
    projectPhase: string;
    deptId: string;
    projectCostLimit: number;
    typeId: string;
    startDate: string;
    endDate: string;
    type: string;
    sort: string;
    parentId: string;
    isMainProject: string;
    projectStatus: string;
    creator: string;
    created: string;
    lastUpdate: string;
    userUpdate: string;
    billable: string;
    contractSize: number;
    licenceAmount: number;
    contractNo: string;
    nameCode: string;
    totalEffort: string;
    percentageComplete: string;
    mainProject: string;
}

export interface IMonthlyEffortProjectMonth {
    january: number;
    february: number;
    march: number;
    april: number;
    may: number;
    june: number;
    july: number;
    august: number;
    september: number;
    october: number;
    november: number;
    december: number;
}

export interface IMonthlyEffortProjectUser {
    project: IMonthlyEffortProjectUserProject;
    totalQuota: number;
    implementQuota: number;
    percentageComplete?: string;
    maintainanceQuota: number;
    previousQuota: number;
    year: number;
    months: IMonthlyEffortProjectMonth;
    totalUsedEffort: number;
    remainingEffort: number;
    billable: string;
}

export interface IMonthlyEffortProjectTotal {
    totalQuota: number;
    implementQuota: number;
    maintainanceQuota: number;
    previousQuota: number;
    year: number;
    months: IMonthlyEffortProjectMonth;
    totalUsedEffort: number;
    remainingEffort: number;
}

export interface IMonthlyEffortProject {
    users: IMonthlyEffortProjectUser[];
    total: IMonthlyEffortProjectTotal;
}

export interface IMonthlyEffortProjectList {
    content: IMonthlyEffortProject;
}

export interface IMonthlyEffortProjectReportList {
    content: IMonthlyEffortProjectReport[];
}

export interface IMonthlyEffortProjectReport {
    id: string;
    firstNamePM: string;
    lastNamePM: string;
    startDate: string;
    lastUpdated: string;
    endDate: string;
    projectName: string;
    projectPhase: string;
    projectProgressAssessment: string;
    status: string;
    userUpdated: string;
    projectType: string;
    department: string;
    userPM?: string;
    userCreated?: string;
}

export interface IProjectReportInfo {
    year: number;
    month: number | object;
    projectId: number | IOption | null;
    userNamePM: string;
    startDate: string;
    endDate: string;
    projectType: string;
    milestoneApproveEntityList: IMilestoneApproveList[];
}
export interface IMilestoneApproveList {
    date: string;
    status: number;
    milestone: string;
    releasePackage: string;
}
export interface IMonthlyReport {
    progressMilestone: IProgressMilestone;
    resource: IResource;
    saleUpSalesEntityList: ISaleUpSalesRequestList[];
    nextPlan: INextPlan[];
    issueRiskEntityList: IIssueRiskRequestList[];
}

export interface IIssueRiskRequestList {
    description: string;
    type: string;
    rootCause: string;
    proposedSolution: string;
}

export interface INextPlan {
    task: string;
    comment: string;
    startDate: string;
    dueDate: string;
}

export interface ISaleUpSalesRequestList {
    oppotunitiesExpand: string;
    changedOfSale: string;
    comment: string;
    potential_revenueVND: number;
}

export interface IProgressMilestone {
    implPhase: string | null;
    progressAssesment: string;
    finishedTasks: string;
    completedMilestones: string;
    workCompleted: number;
    delayNotFinishPlan: string;
    nextMilestone: string;
}

export interface IResource {
    totalHC: number;
    reviewEntityList: IResourceRequestList[];
}

export interface IResourceRequestList {
    description: string;
    resourceReview: string;
}
export interface IMonthlyEffortAddProjectReport {
    id: string;
    projectReportInfo: IProjectReportInfo;
    monthlyReport: IMonthlyReport;
    userUpdated: string;
    userCreated?: string;
}

// Monthly effort member
export interface IId {
    timestamp: number;
    date: Date;
}

export interface IMonthlyEffortMemberInProject {
    id: IId;
    projectId: string;
    projectName: string;
    projectPhase: string;
    deptId: string;
    implementQuota: number;
    maintainQuota: number;
    projectCostLimit: number;
    typeId?: any;
    startDate: Date;
    endDate?: any;
    type: string;
    sort: string;
    parentId: string;
    isMainProject?: any;
    projectStatus: string;
    creator: string;
    created: Date;
    lastUpdate?: any;
    userUpdate?: any;
    billable: string;
    contractSize: number;
    licenceAmount?: any;
    contractNo?: any;
    nameCode?: any;
    totalEffort?: any;
    percentageComplete?: any;
    mainProject?: any;
}

export interface IProjectVerified {
    project: string;
    effort: number;
    effortVerified: number;
    nonPay: number;
    nonPayVerified: number;
    payOT: number;
    payOTVerified: number;
}

export interface IMonthlyEffortMember {
    id: IId;
    userId: string;
    memberCode: string;
    level: string;
    tyear: number;
    tmonth: number;
    tweek: number;
    created: string;
    creator: string;
    department: string;
    firstName: string;
    lastUpdate: string;
    lastName: string;
    onboardDate?: any;
    status: string;
    userupDate: string;
    outboardDate?: any;
    userType?: any;
    title: string;
    rank: string;
    rankCost: string;
    allCcationCost: string;
    effortInMonth: string;
    differenceHours: string;
    project?: any;
    projects: string[];
    hours: string;
    timeStatus?: any;
    projectListVerifieds: IProjectVerified[];
}

export interface IMonthlyEffortMemberList {
    content: IMonthlyEffortMember[];
}

// Monthly effort member project
export interface IMonthlyEffortMemberProject {
    id: IId;
    projectId: string;
    projectName: string;
    projectPhase: string;
    deptId: string;
    implementQuota: number;
    maintainQuota: number;
    projectCostLimit: number;
    typeId?: any;
    startDate: Date;
    endDate?: any;
    type: string;
    sort: number;
    parentId: string;
    isMainProject?: any;
    projectStatus: string;
    creator: string;
    created: Date;
    lastUpdate: Date;
    userUpdate?: any;
    billable: string;
    contractSize: number;
    licenceAmount: number;
    contractNo?: any;
    nameCode: string;
    totalEffort: number;
    percentageComplete?: any;
    mainProject?: any;
}

export interface IMonthlyEffortMemberProjectList {
    content: IMonthlyEffortMemberProject[];
}

// Update effort plan
export interface IEffortPlanDepartment {
    effortplanSent: number | undefined;
    effortplanMissing: number | undefined;
    department: string;
}

export interface IUpdateEffortPlan {
    tyear: string | number;
    tmonth: string | number;
    effortPlanDepts: IEffortPlanDepartment[];
    createdDate?: string;
    userCreate?: string;
    createDate?: string;
    lastUpdateDate?: string;
    userUpdate?: string;
}

export interface IUpdateEffortPlanResponse {
    content: string;
}
