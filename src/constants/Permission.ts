export const MAIN_FUNCTION_LIST: { name: string; functions: string[] }[] = [
    // Synchronize
    { name: '<PERSON>Y<PERSON><PERSON>RONIZ<PERSON>', functions: ['SYNCHRONIZE_DATA'] },
    // System config
    { name: 'ADM_SYS_CONFIG', functions: ['ADM_SYS_CONFIG_VIEW', 'ADM_SYS_CONFIG_EDIT'] },
    // Email config
    { name: 'ADM_EMAIL_CONFIG', functions: ['ADM_EMAIL_CONFIG_VIEW', 'ADM_EMAIL_CONFIG_ADD', 'ADM_EMAIL_CONFIG_EDIT'] },
    // Manage project
    { name: 'ADM_PROJECT', functions: ['ADM_PROJECT_VIEW', 'ADM_PROJECT_ADD', 'ADM_PROJECT_EDIT'] },
    // Manage group
    { name: 'ADM_GROUP', functions: ['ADM_GROUP_VIEW', 'ADM_GROUP_ADD', 'ADM_GROUP_EDIT'] },
    // Manage holidays
    { name: '<PERSON>M_HOL', functions: ['ADM_HOL_VIEW', 'ADM_HOL_ADD', 'ADM_HOL_EDIT'] },
    // Manage user
    { name: 'ADM_USER', functions: ['ADM_USER_VIEW', 'ADM_USER_ADD', 'ADM_USER_EDIT'] },
    // Mamage rank
    { name: 'ADM_RANK', functions: ['ADM_RANK_VIEW', 'ADM_RANK_EDIT'] },
    // Manage special hours
    { name: 'ADM_SPECIAL_HOURS', functions: ['ADM_SPECIAL_HOURS_VIEW', 'ADM_SPECIAL_HOURS_ADD', 'ADM_SPECIAL_HOURS_EDIT'] },
    // CV config Technology
    { name: 'ADM_CVCONFIG', functions: ['ADM_CVCONFIG_LANGUAGE_VIEW', 'ADM_CVCONFIG_LANGUAGE_ADD', 'ADM_CVCONFIG_LANGUAGE_EDIT'] },
    // CV config Language
    { name: 'ADM_CVCONFIG', functions: ['ADM_CVCONFIG_TECHNOLOGY_VIEW', 'ADM_CVCONFIG_TECHNOLOGY_ADD', 'ADM_CVCONFIG_TECHNOLOGY_EDIT'] },
    // CV config Reference
    { name: 'ADM_CVCONFIG', functions: ['ADM_CVCONFIG_REFERENCE_VIEW', 'ADM_CVCONFIG_REFERENCE_ADD', 'ADM_CVCONFIG_REFERENCE_EDIT'] },
    // ExchangeRate config
    { name: 'ADM_EXCHANGERATE', functions: ['ADM_EXCHANGERATE_VIEW', 'ADM_EXCHANGERATE_ADD', 'ADM_EXCHANGERATE_EDIT'] },
    // Manage department
    { name: 'ADM_DEPARTMENT', functions: ['ADM_DEPARTMENT_VIEW', 'ADM_DEPARTMENT_ADD', 'ADM_DEPARTMENT_EDIT'] },
    // Project type config
    {
        name: 'ADM_PROJECT_TYPE_CONFIG',
        functions: ['ADM_PROJECT_TYPE_CONFIG_VIEW', 'ADM_PROJECT_TYPE_CONFIG_ADD', 'ADM_PROJECT_TYPE_CONFIG_EDIT']
    },
    // title config
    {
        name: 'ADM_TITLE_CONFIG',
        functions: ['ADM_TITLE_CONFIG_VIEW', 'ADM_TITLE_CONFIG_ADD', 'ADM_TITLE_CONFIG_EDIT']
    },
    // non-billables config
    {
        name: 'ADM_NON_BILLABLES_CONFIG',
        functions: ['ADM_NON_BILLABLES_CONFIG_VIEW', 'ADM_NON_BILLABLES_CONFIG_EDIT']
    },
    // flexible reporting
    {
        name: 'ADM_FR',
        functions: [
            'ADM_FR_COLUMN_CONFIG_VIEW',
            'ADM_FR_COLUMN_CONFIG_EDIT',
            'ADM_FR_TEXT_CONFIG_VIEW',
            'ADM_FR_TEXT_CONFIG_EDIT',
            'ADM_FR_TEXT_CONFIG_ADD',
            'ADM_FR_TEXT_CONFIG_DELETE',
            'ADM_FR_FLEXIBLE_REPORTING_CONFIG_VIEW',
            'ADM_FR_FLEXIBLE_REPORTING_CONFIG_ADD',
            'ADM_FR_FLEXIBLE_REPORTING_CONFIG_EDIT',
            'ADM_FR_FLEXIBLE_REPORTING_CONFIG_DELETE'
        ]
    },
    // Weekly effort
    {
        name: 'RP_WER',
        functions: [
            'RP_WER_MEMBER_VIEW',
            'RP_WER_PROJECT_VIEW',
            'RP_WER_PROJECT_DETAIL_VIEW',
            'RP_WER_PROJECT_DETAIL_PM_VERIFY',
            'RP_WER_PROJECT_DETAIL_QA_VERIFY',
            'RP_WER_DOWNLOAD',
            'RP_WER_MEMBER_COMMENT'
        ]
    },
    // Non-billable
    {
        name: 'RP_NONBILL',
        functions: [
            'RP_NONBILL_MEMBER_VIEW',
            'RP_NONBILL_CHART_VIEW',
            'RP_NONBILL_DOWNLOAD',
            'RP_NONBILL_WARNING_MEMBER_VIEW',
            'RP_NONBILL_COMMENT',
            'RP_NONBILL_COMMENT_DETAIL'
        ]
    },
    // Resources in projects
    {
        name: 'RP_RIP',
        functions: ['RP_RIP_LISTHC_VIEW', 'RP_RIP_DOWNLOAD', 'RP_RIP_LISTHC_COMMENT', 'RP_RIP_LISTHC_COMMENT_DETAIL']
    },
    // Cost monitoring
    { name: 'RP_WCR', functions: ['RP_WCR_MC_VIEW', 'RP_WCR_EFW_VIEW', 'RP_WCR_DOWNLOAD', 'RP_WCR_MC_COMMENT'] },
    {
        name: 'RP_MER',
        functions: [
            'RP_MER_SUM_VIEW',
            'RP_MER_SUM_EDIT',
            'RP_MER_PROJECT_VIEW',
            'RP_MER_MEMBER_MEMBERS_VIEW',
            'RP_MER_MEMBER_PROJECTS_VIEW',
            'RP_MER_PROJECT_REPORT_VIEW',
            'RP_MER_PROJECT_REPORT_ADD',
            'RP_MER_PROJECT_REPORT_EDIT',
            'RP_MER_PROJECT_REPORT_DOWNLOAD',
            'RP_MER_ORM_REPORT_VIEW',
            'RP_MER_ORM_REPORT_ADD',
            'RP_MER_ORM_REPORT_EXPORT',
            'RP_MER_ORM_REPORT_DELETE',
            'RP_MER_PROJECT_COMMENT',
            'RP_MER_SUM_DOWNLOAD',
            'RP_MER_PROJECT_DOWNLOAD',
            'RP_MER_MEMBER_DOWNLOAD'
        ]
    },

    {
        name: 'RP_GR',
        functions: ['RP_GR_PROJECT_REPORT_VIEW', 'RP_GR_ORM_REPORT_VIEW', 'RP_GR_PRODUCT_REPORT_VIEW']
    },
    // Monthly project cost
    {
        name: 'RP_MPC',
        functions: [
            'RP_MPC_SUM_VIEW',
            'RP_MPC_DBM_VIEW',
            'RP_MPC_MCD_VIEW',
            'RP_MPC_DOWNLOAD',
            'RP_MPC_MCD_ADD',
            'RP_MPC_MCD_EDIT',
            'RP_MPC_MCD_DOWNLOAD_TEMPLATE',
            'RP_MPC_MCD_IMPORT_TEMPLATE'
        ]
    },
    // Sale
    {
        name: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE',
        functions: [
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_VIEW',
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_EDIT',
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_DOWNLOAD',
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_EDIT_ROWS'
        ]
    },
    {
        name: 'SALE_SALE_LIST',
        functions: [
            'SALE_SALE_LIST_REQUEST_VIEW',
            'SALE_SALE_LIST_REQUEST_ADD',
            'SALE_SALE_LIST_REQUEST_EDIT',
            'SALE_SALE_LIST_REQUEST_DELETE',
            'SALE_SALE_LIST_REQUEST_DOWNLOAD',
            'SALE_SALE_LIST_SUPPLIER_VIEW',
            'SALE_SALE_LIST_SUPPLIER_ADD',
            'SALE_SALE_LIST_SUPPLIER_EDIT',
            'SALE_SALE_LIST_SUPPLIER_DELETE',
            'SALE_SALE_LIST_SUPPLIER_DOWNLOAD'
        ]
    },
    {
        name: 'SALE_MONITOR_BIDDING_PACKAGE',
        functions: [
            'SALE_PACKAGE_BIDDING_TRACKING_VIEW',
            'SALE_PACKAGE_BIDDING_TRACKING_SYNCHRONIZE',
            'SALE_PACKAGE_BIDDING_TRACKING_DOWNLOAD',
            'SALE_PACKAGE_BIDDING_REPORT_VIEW',
            'SALE_PACKAGE_BIDDING_REPORT_ADD',
            'SALE_PACKAGE_BIDDING_REPORT_EDIT'
        ]
    },
    {
        name: 'SALE_PROJECT_REFERENCE',
        functions: ['SALE_PROJECT_REFERENCE_VIEW', 'SALE_PROJECT_REFERENCE_DOWNLOAD']
    },
    {
        name: 'RP_PAYMENT_STATUS',
        functions: ['RP_PAYMENT_STATUS_VIEW', 'RP_PAYMENT_STATUS_EDIT', 'RP_PAYMENT_STATUS_ADD']
    },
    // Sale pipeline
    {
        name: 'SALE_SALEPIPELINE_SUMMARY',
        functions: ['SALE_SALEPIPELINE_SUMMARY_VIEW', 'SALE_SALEPIPELINE_SUMMARY_DOWNLOAD', 'SALE_SALEPIPELINE_SUMMARY_EDIT_ROWS']
    },
    {
        name: 'SALE_SALEPIPELINE_ONGOING',
        functions: [
            'SALE_SALEPIPELINE_ONGOING_VIEW',
            'SALE_SALEPIPELINE_ONGOING_ADD',
            'SALE_SALEPIPELINE_ONGOING_EDIT',
            'SALE_SALEPIPELINE_ONGOING_DELETE',
            'SALE_SALEPIPELINE_ONGOING_EDIT_ROWS'
        ]
    },
    {
        name: 'SALE_SALEPIPELINE_BIDDING',
        functions: [
            'SALE_SALEPIPELINE_BIDDING_VIEW',
            'SALE_SALEPIPELINE_BIDDING_ADD',
            'SALE_SALEPIPELINE_BIDDING_EDIT',
            'SALE_SALEPIPELINE_BIDDING_DELETE',
            'SALE_SALEPIPELINE_BIDDING_EDIT_ROWS'
        ]
    },
    {
        name: 'SALE_SALEPIPELINE_BUDGETING',
        functions: ['SALE_SALEPIPELINE_BUDGETING_VIEW', 'SALE_SALEPIPELINE_BUDGETING_EDIT', 'SALE_SALEPIPELINE_BUDGETING_EDIT_ROWS']
    },
    //Register Working calendar
    {
        name: 'HR_WORKING_CALENDAR',
        functions: [
            'HR_WORKING_CALENDAR_VIEW',
            'HR_WORKING_CALENDAR_EDIT',
            'HR_WORKING_CALENDAR_EXPORT',
            'HR_WORKING_CALENDAR_VERIFY',
            'HR_WORKING_CALENDAR_APROVE',
            'HR_WORKING_CALENDAR_CLOSING'
        ]
    },
    // Manage Leave Day
    {
        name: 'HR_LEAVE_DAY',
        functions: ['HR_LEAVE_DAY_VIEW', 'HR_LEAVE_DAY_EDIT']
    },
    // Manage ot
    {
        name: 'HR_OT_REQUESTS',
        functions: ['HR_OT_VIEW', 'HR_OT_ADD', 'HR_OT_EDIT', 'HR_OT_APROVE']
    },
    // Manage leaves
    {
        name: 'HR_LEAVES',
        functions: ['HR_LEAVES_VIEW', 'HR_LEAVES_ADD', 'HR_LEAVES_EDIT', 'HR_LEAVES_APPROVE']
    },
    // Manage resignation
    {
        name: 'HR_RESIGNATION',
        functions: ['HR_RESIGNATION_VIEW', 'HR_RESIGNATION_ADD', 'HR_RESIGNATION_EDIT', 'HR_RESIGNATION_APPROVE']
    },
    // Skill manage
    {
        name: 'SKILL_UPDATE',
        functions: [
            'SKILL_MANAGE_SKILL_UPDATE_VIEW',
            'SKILL_MANAGE_SKILL_UPDATE_ADD',
            'SKILL_MANAGE_SKILL_UPDATE_EDIT',
            'SKILL_MANAGE_SKILL_UPDATE_DOWNLOAD',
            'SKILL_MANAGE_SKILL_UPDATE_VIEWCV'
        ]
    },
    {
        name: 'SKILL_REPORT',
        functions: ['SKILL_MANAGE_SKILL_REPORT_VIEW', 'SKILL_MANAGE_SKILL_REPORT_DOWNLOAD']
    },
    {
        name: 'RP_PRODUCT',
        functions: ['RP_PRODUCT_VIEW']
    }
];

// Permission
export const PERMISSIONS = {
    // Synchronize
    syn: {
        synchronize: 'SYNCHRONIZE_DATA'
    },
    // Admin
    admin: {
        userPermission: {
            add: 'ADM_USER_ADD',
            edit: 'ADM_USER_EDIT'
        },
        projectPermission: {
            add: 'ADM_PROJECT_ADD',
            edit: 'ADM_PROJECT_EDIT'
        },
        groupPermission: {
            add: 'ADM_GROUP_ADD',
            edit: 'ADM_GROUP_EDIT'
        },
        holidayPermission: {
            add: 'ADM_HOL_ADD',
            edit: 'ADM_HOL_EDIT'
        },
        rankPermission: {
            edit: 'ADM_RANK_EDIT'
        },
        systemConfigPermission: {
            edit: 'ADM_SYS_CONFIG_EDIT'
        },
        specialHoursPermission: {
            add: 'ADM_SPECIAL_HOURS_ADD',
            edit: 'ADM_SPECIAL_HOURS_EDIT'
        },
        emailConfigPermission: {
            add: 'ADM_EMAIL_CONFIG_ADD',
            edit: 'ADM_EMAIL_CONFIG_EDIT'
        },
        cVConfigTechnologyPermission: {
            add: 'ADM_CVCONFIG_TECHNOLOGY_ADD',
            edit: 'ADM_CVCONFIG_TECHNOLOGY_EDIT'
        },
        cVConfigLanguagePermission: {
            add: 'ADM_CVCONFIG_LANGUAGE_ADD',
            edit: 'ADM_CVCONFIG_LANGUAGE_EDIT'
        },
        cVConfigReferencePermission: {
            add: 'ADM_CVCONFIG_REFERENCE_ADD',
            edit: 'ADM_CVCONFIG_REFERENCE_EDIT'
        },
        exchangeRatePermission: {
            add: 'ADM_EXCHANGERATE_ADD',
            edit: 'ADM_EXCHANGERATE_EDIT'
        },
        departmentPermission: {
            add: 'ADM_DEPARTMENT_ADD',
            edit: 'ADM_DEPARTMENT_EDIT'
        },
        projectTypeConfigPermission: {
            add: 'ADM_PROJECT_TYPE_CONFIG_ADD',
            edit: 'ADM_PROJECT_TYPE_CONFIG_EDIT'
        },
        titleConfigPermission: {
            add: 'ADM_TITLE_CONFIG_ADD',
            edit: 'ADM_TITLE_CONFIG_EDIT'
        },
        nonBillablesConfigPermission: {
            edit: 'ADM_NON_BILLABLES_CONFIG_EDIT'
        },
        flexibleReportingConfigPermission: {
            columnConfig: {
                edit: 'ADM_FR_COLUMN_CONFIG_EDIT'
            },
            textConfig: {
                edit: 'ADM_FR_TEXT_CONFIG_EDIT',
                add: 'ADM_FR_TEXT_CONFIG_ADD',
                delete: 'ADM_FR_TEXT_CONFIG_DELETE'
            },
            flexibleReportConfig: {
                add: 'ADM_FR_FLEXIBLE_REPORTING_CONFIG_ADD',
                edit: 'ADM_FR_FLEXIBLE_REPORTING_CONFIG_EDIT',
                delete: 'ADM_FR_FLEXIBLE_REPORTING_CONFIG_DELETE'
            }
        }
    },
    // working calendar
    workingCalendar: {
        registerWorkingCalendar: {
            viewWorkingCalendar: 'HR_WORKING_CALENDAR_VIEW',
            editWorkingCalendar: 'HR_WORKING_CALENDAR_EDIT',
            exportWorkingCalendar: 'HR_WORKING_CALENDAR_EXPORT',
            verifyWorkingCalendar: 'HR_WORKING_CALENDAR_VERIFY',
            aproveWorkingCalendar: 'HR_WORKING_CALENDAR_APROVE',
            closingWorkingCalendar: 'HR_WORKING_CALENDAR_CLOSING'
        },
        manageLeaveDay: {
            viewManageLeaveDay: 'HR_LEAVE_DAY_VIEW',
            editManageLeaveDay: 'HR_LEAVE_DAY_EDIT'
        },
        manageOt: {
            view: 'HR_OT_VIEW',
            add: 'HR_OT_ADD',
            edit: 'HR_OT_EDIT',
            approve: 'HR_OT_APROVE',
            delete: 'HR_OT_DELETE'
        },
        manageLeaves: {
            view: 'HR_LEAVES_VIEW',
            add: 'HR_LEAVES_ADD',
            edit: 'HR_LEAVES_EDIT',
            approve: 'HR_LEAVES_APPROVE',
            delete: 'HR_LEAVES_DELETE'
        },
        manageResignation: {
            view: 'HR_RESIGNATION_VIEW',
            add: 'HR_RESIGNATION_ADD',
            edit: 'HR_RESIGNATION_EDIT',
            approve: 'HR_RESIGNATION_APPROVE'
        }
    },
    // Report
    report: {
        weeklyEffort: {
            viewMember: 'RP_WER_MEMBER_VIEW',
            viewProject: 'RP_WER_PROJECT_VIEW',
            viewProjectDetail: 'RP_WER_PROJECT_DETAIL_VIEW',
            pmVerify: 'RP_WER_PROJECT_DETAIL_PM_VERIFY',
            qaVerify: 'RP_WER_PROJECT_DETAIL_QA_VERIFY',
            download: 'RP_WER_DOWNLOAD',
            comment: 'RP_WER_MEMBER_COMMENT'
        },

        generalReport: {
            projectReport: {
                add: 'RP_GR_PROJECT_REPORT_ADD',
                edit: 'RP_GR_PROJECT_REPORT_EDIT',
                download: 'RP_GR_PROJECT_REPORT_DOWNLOAD',
                view: 'RP_GR_PROJECT_REPORT_VIEW',
                delete: 'RP_GR_PROJECT_REPORT_DELETE'
            },
            viewORMReport: 'RP_GR_ORM_REPORT_VIEW',
            uploadORMReport: 'RP_GR_ORM_REPORT_ADD',
            downloadORMReport: 'RP_GR_ORM_REPORT_EXPORT',
            deleteORMReport: 'RP_GR_ORM_REPORT_DELETE'
        },
        monthlyEffort: {
            viewSummary: 'RP_MER_SUM_VIEW',
            viewProject: 'RP_MER_PROJECT_VIEW',
            viewDepartmentMember: {
                viewTabMembes: 'RP_MER_MEMBER_MEMBERS_VIEW',
                viewTabProjects: 'RP_MER_MEMBER_PROJECTS_VIEW'
            },
            editEffortPlan: 'RP_MER_SUM_EDIT',
            summaryDownload: 'RP_MER_SUM_DOWNLOAD',
            projectDownload: 'RP_MER_PROJECT_DOWNLOAD',
            memberDownload: 'RP_MER_MEMBER_DOWNLOAD',
            comment: 'RP_MER_PROJECT_COMMENT'
        },
        nonBillable: {
            viewMember: 'RP_NONBILL_MEMBER_VIEW',
            viewChart: 'RP_NONBILL_CHART_VIEW',
            viewWarning: 'RP_NONBILL_WARNING_MEMBER_VIEW',
            download: 'RP_NONBILL_DOWNLOAD',
            comment: 'RP_NONBILL_COMMENT',
            commentDetail: 'RP_NONBILL_COMMENT_DETAIL'
        },
        ResourcesInProjects: {
            viewList: 'RP_RIP_LISTHC_VIEW',
            download: 'RP_RIP_DOWNLOAD',
            comment: 'RP_RIP_LISTHC_COMMENT',
            commentDetail: 'RP_RIP_LISTHC_COMMENT_DETAIL'
        },
        costMonitoring: {
            download: 'RP_WCR_DOWNLOAD',
            comment: 'RP_WCR_MC_COMMENT'
        },
        monthlyProjectCost: {
            addCost: 'RP_MPC_MCD_ADD',
            download: 'RP_MPC_DOWNLOAD',
            downloadTemplate: 'RP_MPC_MCD_DOWNLOAD_TEMPLATE',
            importTemplate: 'RP_MPC_MCD_IMPORT_TEMPLATE'
        },
        skillManage: {
            skillsUpdate: {
                view: 'SKILL_MANAGE_SKILL_UPDATE_VIEW',
                add: 'SKILL_MANAGE_SKILL_UPDATE_ADD',
                edit: 'SKILL_MANAGE_SKILL_UPDATE_EDIT',
                download: 'SKILL_MANAGE_SKILL_UPDATE_DOWNLOAD',
                viewCV: 'SKILL_MANAGE_SKILL_UPDATE_VIEWCV'
            },
            skillsReportPermission: {
                view: 'SKILL_MANAGE_SKILL_REPORT_VIEW',
                download: 'SKILL_MANAGE_SKILL_REPORT_DOWNLOAD'
            }
        },
        cvConfig: {
            viewTechnology: 'ADM_CVCONFIG_TECHNOLOGY_VIEW',
            addTechnology: 'ADM_CVCONFIG_TECHNOLOGY_ADD',
            editTechnology: 'ADM_CVCONFIG_TECHNOLOGY_EDIT',
            viewLanguage: 'ADM_CVCONFIG_LANGUAGE_VIEW',
            addLanguage: 'ADM_CVCONFIG_LANGUAGE_ADD',
            editLanguage: 'ADM_CVCONFIG_LANGUAGE_EDIT',
            viewReference: 'ADM_CVCONFIG_REFERENCE_VIEW',
            addReference: 'ADM_CVCONFIG_REFERENCE_ADD',
            editReference: 'ADM_CVCONFIG_REFERENCE_EDIT'
        },
        exChangeRateConfig: {
            viewExchangeRate: 'ADM_EXCHANGERATE_VIEW',
            addExchangeRate: 'ADM_EXCHANGERATE_ADD',
            editExchangeRate: 'ADM_EXCHANGERATE_EDIT'
        }
    },
    // Sale
    sale: {
        saleList: {
            viewRequests: 'SALE_SALE_LIST_REQUEST_VIEW',
            addRequest: 'SALE_SALE_LIST_REQUEST_ADD',
            editRequest: 'SALE_SALE_LIST_REQUEST_EDIT',
            downloadRequest: 'SALE_SALE_LIST_REQUEST_DOWNLOAD',
            viewSupplier: 'SALE_SALE_LIST_SUPPLIER_VIEW',
            addSupplier: 'SALE_SALE_LIST_SUPPLIER_ADD',
            editSupplier: 'SALE_SALE_LIST_SUPPLIER_EDIT',
            downloadSupplier: 'SALE_SALE_LIST_SUPPLIER_DOWNLOAD'
        },
        monthlyProductionPerformancePermission: {
            view: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE_VIEW',
            edit: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE_EDIT',
            delete: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE_DELETE',
            download: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE_DOWNLOAD',
            editRows: 'SALE_MONTHLY_PRODUCTION_PERFORMANCE_EDIT_ROWS'
        },
        monitorBiddingPackage: {
            viewPackagesBiddingTracking: 'SALE_PACKAGE_BIDDING_TRACKING_VIEW',
            synchronizePackagesBiddingTracking: 'SALE_PACKAGE_BIDDING_TRACKING_SYNCHRONIZE',
            downloadPackagesBiddingtracking: 'SALE_PACKAGE_BIDDING_TRACKING_DOWNLOAD',
            viewPackagesBiddingReport: 'SALE_PACKAGE_BIDDING_REPORT_VIEW',
            add: 'SALE_PACKAGE_BIDDING_REPORT_ADD',
            edit: 'SALE_PACKAGE_BIDDING_REPORT_EDIT'
        },
        projectReferencePermission: {
            view: 'SALE_PROJECT_REFERENCE_VIEW',
            download: 'SALE_PROJECT_REFERENCE_DOWNLOAD'
        },
        salePipeline: {
            summaryPermission: {
                view: 'SALE_SALEPIPELINE_SUMMARY_VIEW',
                download: 'SALE_SALEPIPELINE_SUMMARY_DOWNLOAD',
                editRows: 'SALE_SALEPIPELINE_SUMMARY_EDIT_ROWS'
            },
            onGoingPermission: {
                view: 'SALE_SALEPIPELINE_ONGOING_VIEW',
                add: 'SALE_SALEPIPELINE_ONGOING_ADD',
                edit: 'SALE_SALEPIPELINE_ONGOING_EDIT',
                delete: 'SALE_SALEPIPELINE_ONGOING_DELETE',
                editRows: 'SALE_SALEPIPELINE_ONGOING_EDIT_ROWS'
            },
            biddingPermission: {
                view: 'SALE_SALEPIPELINE_BIDDING_VIEW',
                add: 'SALE_SALEPIPELINE_BIDDING_ADD',
                edit: 'SALE_SALEPIPELINE_BIDDING_EDIT',
                delete: 'SALE_SALEPIPELINE_BIDDING_DELETE',
                editRows: 'SALE_SALEPIPELINE_BIDDING_EDIT_ROWS'
            },
            budgetingPermission: {
                view: 'SALE_SALEPIPELINE_BUDGETING_VIEW',
                edit: 'SALE_SALEPIPELINE_BUDGETING_EDIT',
                editRows: 'SALE_SALEPIPELINE_BUDGETING_EDIT_ROWS'
            }
        }
    }
};

// VIEW
export const MAIN_FUNCTIONS = {
    // Synchronize
    syn: {
        root: ['SYNCHRONIZE_DATA']
    },
    // Admin
    admin: {
        root: [
            'ADM_PROJECT_VIEW',
            'ADM_GROUP_VIEW',
            'ADM_HOL_VIEW',
            'ADM_USER_VIEW',
            'ADM_RANK_VIEW',
            'ADM_SYS_CONFIG_VIEW',
            'ADM_SPECIAL_HOURS_VIEW',
            'ADM_EMAIL_CONFIG_VIEW',
            'ADM_CVCONFIG_TECHNOLOGY_VIEW',
            'ADM_CVCONFIG_LANGUAGE_VIEW',
            'ADM_CVCONFIG_REFERENCE_VIEW',
            'ADM_DEPARTMENT_VIEW',
            'ADM_PROJECT_TYPE_CONFIG_VIEW',
            'ADM_TITLE_CONFIG_VIEW',
            'ADM_NON_BILLABLES_CONFIG_VIEW',
            'ADM_FR_COLUMN_CONFIG_VIEW',
            'ADM_FR_TEXT_CONFIG_VIEW'
        ],
        project: ['ADM_PROJECT_VIEW'],
        group: ['ADM_GROUP_VIEW'],
        holidays: ['ADM_HOL_VIEW'],
        user: ['ADM_USER_VIEW'],
        rank: ['ADM_RANK_VIEW'],
        systemConfig: ['ADM_SYS_CONFIG_VIEW'],
        emailConfig: ['ADM_EMAIL_CONFIG_VIEW'],
        cvConfig: ['ADM_CVCONFIG_TECHNOLOGY_VIEW', 'ADM_CVCONFIG_LANGUAGE_VIEW', 'ADM_CVCONFIG_REFERENCE_VIEW'],
        exChangeRateConfig: ['ADM_EXCHANGERATE_VIEW'],
        specialHours: ['ADM_SPECIAL_HOURS_VIEW'],
        department: ['ADM_DEPARTMENT_VIEW'],
        projectTypeConfig: ['ADM_PROJECT_TYPE_CONFIG_VIEW'],
        titleConfig: ['ADM_TITLE_CONFIG_VIEW'],
        nonBillablesConfig: ['ADM_NON_BILLABLES_CONFIG_VIEW'],
        flexibleReporting: {
            root: ['ADM_FR_COLUMN_CONFIG_VIEW', 'ADM_FR_TEXT_CONFIG_VIEW', 'ADM_FR_FLEXIBLE_REPORTING_CONFIG_VIEW'],
            columnConfig: ['ADM_FR_COLUMN_CONFIG_VIEW'],
            textConfig: ['ADM_FR_TEXT_CONFIG_VIEW'],
            flexibleReportingConfig: ['ADM_FR_FLEXIBLE_REPORTING_CONFIG_VIEW']
        }
    },
    // Working calendar
    workingCalendar: {
        root: ['HR_WORKING_CALENDAR_VIEW', 'HR_LEAVE_DAY_VIEW', 'HR_RESIGNATION_VIEW', 'HR_LEAVES_VIEW', 'HR_OT_VIEW'],
        registerWorkingCalendar: ['HR_WORKING_CALENDAR_VIEW'],
        manageLeaveDays: ['HR_LEAVE_DAY_VIEW'],
        manageOt: ['HR_OT_VIEW'],
        manageLeaves: ['HR_LEAVES_VIEW'],
        manageResignation: ['HR_RESIGNATION_VIEW']
    },
    // Report
    reports: {
        root: [
            'RP_GR_PROJECT_REPORT_VIEW',
            'RP_GR_ORM_REPORT_VIEW',
            'RP_GR_PRODUCT_REPORT_VIEW',
            'RP_WER_MEMBER_VIEW',
            'RP_WER_PROJECT_VIEW',
            'RP_WER_PROJECT_DETAIL_VIEW',
            'RP_MER_SUM_VIEW',
            'RP_MER_PROJECT_VIEW',
            'RP_MER_MEMBER_MEMBERS_VIEW',
            'RP_MER_MEMBER_PROJECTS_VIEW',
            'RP_MER_PROJECT_REPORT_VIEW',
            'RP_NONBILL_MEMBER_VIEW',
            'RP_NONBILL_CHART_VIEW',
            'RP_NONBILL_WARNING_MEMBER_VIEW',
            'RP_RIP_LISTHC_VIEW',
            'RP_WCR_MC_VIEW',
            'RP_WCR_EFW_VIEW',
            'RP_MPC_SUM_VIEW',
            'RP_MPC_DBM_VIEW',
            'RP_MPC_MCD_VIEW',
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_VIEW',
            'SALE_SALE_LIST_REQUEST_VIEW',
            'SALE_SALE_LIST_SUPPLIER_VIEW',
            'SALE_SALEPIPELINE_SUMMARY_VIEW',
            'SALE_SALEPIPELINE_ONGOING_VIEW',
            'SALE_SALEPIPELINE_BIDDING_VIEW',
            'SALE_SALEPIPELINE_BUDGETING_VIEW',
            'SKILL_MANAGE_SKILL_UPDATE_VIEW',
            'SKILL_MANAGE_SKILL_REPORT_VIEW'
        ],
        weeklyEffort: {
            root: ['RP_WER_MEMBER_VIEW', 'RP_WER_PROJECT_VIEW', 'RP_WER_PROJECT_DETAIL_VIEW'],
            member: ['RP_WER_MEMBER_VIEW'],
            project: ['RP_WER_PROJECT_VIEW'],
            projectDetail: ['RP_WER_PROJECT_DETAIL_VIEW']
        },
        monthlyEffort: {
            root: ['RP_MER_SUM_VIEW', 'RP_MER_PROJECT_VIEW', 'RP_MER_MEMBER_MEMBERS_VIEW', 'RP_MER_MEMBER_PROJECTS_VIEW'],
            summary: ['RP_MER_SUM_VIEW'],
            project: ['RP_MER_PROJECT_VIEW'],
            member: ['RP_MER_MEMBER_MEMBERS_VIEW', 'RP_MER_MEMBER_PROJECTS_VIEW']
        },
        generalReport: {
            root: ['RP_GR_PROJECT_REPORT_VIEW', 'RP_GR_ORM_REPORT_VIEW', 'RP_GR_PRODUCT_REPORT_VIEW'],
            productReport: ['RP_GR_PRODUCT_REPORT_VIEW'],
            project_report: ['RP_GR_PROJECT_REPORT_VIEW'],
            reportORM: ['RP_GR_ORM_REPORT_VIEW']
        },
        nonBillable: {
            root: ['RP_NONBILL_MEMBER_VIEW', 'RP_NONBILL_CHART_VIEW', 'RP_NONBILL_WARNING_MEMBER_VIEW'],
            nonBillMember: ['RP_NONBILL_MEMBER_VIEW', 'RP_NONBILL_WARNING_MEMBER_VIEW'],
            nonBillChart: ['RP_NONBILL_CHART_VIEW']
        },
        resourcesInProjects: ['RP_RIP_LISTHC_VIEW'],
        costMonitoring: { root: ['RP_WCR_MC_VIEW', 'RP_WCR_EFW_VIEW'], monthlyCost: ['RP_WCR_MC_VIEW'], weeklyCost: ['RP_WCR_EFW_VIEW'] },
        monthlyProjectCost: {
            root: ['RP_MPC_SUM_VIEW', 'RP_MPC_DBM_VIEW', 'RP_MPC_MCD_VIEW'],
            summary: ['RP_MPC_SUM_VIEW'],
            detailByMonth: ['RP_MPC_DBM_VIEW'],
            monthlyCost: ['RP_MPC_MCD_VIEW']
        },
        skillManage: {
            root: ['SKILL_MANAGE_SKILL_UPDATE_VIEW', 'SKILL_MANAGE_SKILL_REPORT_VIEW'],
            skillsUpdate: ['SKILL_MANAGE_SKILL_UPDATE_VIEW'],
            skillsReport: ['SKILL_MANAGE_SKILL_REPORT_VIEW']
        }
    },
    // Sale
    sale: {
        root: [
            'SALE_MONTHLY_PRODUCTION_PERFORMANCE_VIEW',
            'SALE_SALE_LIST_REQUEST_VIEW',
            'SALE_SALE_LIST_SUPPLIER_VIEW',
            'SALE_SALEPIPELINE_SUMMARY_VIEW',
            'SALE_SALEPIPELINE_ONGOING_VIEW',
            'SALE_SALEPIPELINE_BIDDING_VIEW',
            'SALE_SALEPIPELINE_BUDGETING_VIEW',
            'SALE_PROJECT_REFERENCE_VIEW',
            'RP_PAYMENT_STATUS_VIEW'
        ],
        saleLead: ['SALE_SALE_LIST_REQUEST_VIEW', 'SALE_SALE_LIST_SUPPLIER_VIEW'],
        monitorBiddingPackage: ['SALE_PACKAGE_BIDDING_TRACKING_VIEW', 'SALE_PACKAGE_BIDDING_REPORT_VIEW'],
        monthlyProductionPerformance: ['SALE_MONTHLY_PRODUCTION_PERFORMANCE_VIEW'],
        salePipeline: {
            root: [
                'SALE_SALEPIPELINE_SUMMARY_VIEW',
                'SALE_SALEPIPELINE_ONGOING_VIEW',
                'SALE_SALEPIPELINE_BIDDING_VIEW',
                'SALE_SALEPIPELINE_BUDGETING_VIEW'
            ],
            summary: ['SALE_SALEPIPELINE_SUMMARY_VIEW'],
            onGoing: ['SALE_SALEPIPELINE_ONGOING_VIEW'],
            bidding: ['SALE_SALEPIPELINE_BIDDING_VIEW'],
            budget: ['SALE_SALEPIPELINE_BUDGETING_VIEW']
        },
        projectReference: ['SALE_PROJECT_REFERENCE_VIEW'],
        paymentStatus: ['RP_PAYMENT_STATUS_VIEW']
    }
};
