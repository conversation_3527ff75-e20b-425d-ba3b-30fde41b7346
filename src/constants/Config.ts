// project imports
import { LAYOUT_CONST } from './Common';
import { SPACING, BORDER_RADIUS, CONTAINER_WIDTHS } from './Spacing';

// Constants
export const BASE_PATH = '';
export const DASHBOARD_PATH = '/';
export const HORIZONTAL_MAX_ITEM = 6;
export const SEARCH_TIMESTAMP = 'SEARCH_TIMESTAMP';

// types
export interface ConfigProps {
    layout: string;
    drawerType: string;
    fontFamily: string;
    borderRadius: number;
    outlinedFilled: boolean;
    navType: 'light' | 'dark';
    presetColor: string;
    locale: string;
    container: boolean;
    // New spacing system properties
    spacing: typeof SPACING;
    containerWidths: typeof CONTAINER_WIDTHS;
    borderRadiusScale: typeof BORDER_RADIUS;
}

const Config: ConfigProps = {
    layout: LAYOUT_CONST.VERTICAL_LAYOUT, // vertical, horizontal
    drawerType: LAYOUT_CONST.DEFAULT_DRAWER, // default, mini-drawer
    fontFamily: `'Jost', 'Helvetica', 'Arial', sans-serif`,
    borderRadius: BORDER_RADIUS.md, // 8px - primary border radius
    outlinedFilled: true,
    navType: 'light', // light, dark
    presetColor: 'default', // default, theme1, theme2
    locale: 'en', // 'en' - English, 'vi' - Vietnamese
    container: false,

    // Spacing System Integration
    spacing: SPACING,
    containerWidths: CONTAINER_WIDTHS,
    borderRadiusScale: BORDER_RADIUS
};

export default Config;
