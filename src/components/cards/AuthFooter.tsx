// material-ui
import { Link, Typography, Stack } from '@mui/material';

// ==============================|| AUTH FOOTER ||============================== //

const AuthFooter = () => {
    const year = new Date().getFullYear();

    return (
        <Stack direction="row" justifyContent="center">
            <Typography
                variant="subtitle2"
                component={Link}
                href="#"
                target="_blank"
                underline="hover"
                sx={{
                    cursor: 'pointer',
                    color: 'text.secondary',
                    fontSize: '0.875rem',
                    fontWeight: 400
                }}
            >
                © {year} - WorkFinder. All rights reserved.
            </Typography>
        </Stack>
    );
};

export default AuthFooter;
