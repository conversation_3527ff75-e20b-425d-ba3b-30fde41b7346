const RegisterWorkingCalendar = () => {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M8 5.75C7.59 5.75 7.25 5.41 7.25 5V2C7.25 1.59 7.59 1.25 8 1.25C8.41 1.25 8.75 1.59 8.75 2V5C8.75 5.41 8.41 5.75 8 5.75Z"
                fill="#292D32"
            />
            <path
                d="M16 5.75C15.59 5.75 15.25 5.41 15.25 5V2C15.25 1.59 15.59 1.25 16 1.25C16.41 1.25 16.75 1.59 16.75 2V5C16.75 5.41 16.41 5.75 16 5.75Z"
                fill="#292D32"
            />
            <path
                d="M8.5 14.4999C8.24 14.4999 7.98 14.3899 7.79 14.2099C7.61 14.0199 7.5 13.7699 7.5 13.4999C7.5 13.3699 7.53 13.2399 7.58 13.1199C7.63 12.9999 7.7 12.89 7.79 12.79C8.16 12.42 8.83 12.42 9.21 12.79C9.39 12.98 9.5 13.2399 9.5 13.4999C9.5 13.5599 9.49 13.63 9.48 13.7C9.47 13.76 9.45 13.8199 9.42 13.8799C9.4 13.9399 9.37 13.9999 9.33 14.0599C9.29 14.1099 9.25 14.1599 9.21 14.2099C9.02 14.3899 8.76 14.4999 8.5 14.4999Z"
                fill="#292D32"
            />
            <path
                d="M12 14.5001C11.87 14.5001 11.74 14.4701 11.62 14.4201C11.49 14.3701 11.39 14.3001 11.29 14.2101C11.11 14.0201 11 13.7701 11 13.5001C11 13.3701 11.03 13.2401 11.08 13.1201C11.13 13.0001 11.2 12.8901 11.29 12.7901C11.39 12.7001 11.49 12.6301 11.62 12.5801C11.99 12.4301 12.43 12.5101 12.71 12.7901C12.89 12.9801 13 13.2401 13 13.5001C13 13.5601 12.99 13.6301 12.98 13.7001C12.97 13.7601 12.95 13.8201 12.92 13.8801C12.9 13.9401 12.87 14.0001 12.83 14.0601C12.8 14.1101 12.75 14.1601 12.71 14.2101C12.52 14.3901 12.26 14.5001 12 14.5001Z"
                fill="#292D32"
            />
            <path
                d="M8.5 18C8.37 18 8.24 17.97 8.12 17.92C7.99 17.87 7.88 17.8 7.79 17.71C7.7 17.62 7.63 17.51 7.58 17.38C7.53 17.26 7.5 17.13 7.5 17C7.5 16.87 7.53 16.74 7.58 16.62C7.63 16.49 7.7 16.38 7.79 16.29C7.88 16.2 7.99 16.13 8.12 16.08C8.36 15.98 8.64 15.97 8.88 16.08C9.01 16.13 9.12 16.2 9.21 16.29C9.3 16.38 9.37 16.49 9.42 16.62C9.47 16.74 9.5 16.87 9.5 17C9.5 17.13 9.47 17.26 9.42 17.38C9.37 17.51 9.3 17.62 9.21 17.71C9.12 17.8 9.01 17.87 8.88 17.92C8.76 17.97 8.63 18 8.5 18Z"
                fill="#292D32"
            />
            <path
                d="M20.5 9.83997H3.5C3.09 9.83997 2.75 9.49997 2.75 9.08997C2.75 8.67997 3.09 8.33997 3.5 8.33997H20.5C20.91 8.33997 21.25 8.67997 21.25 9.08997C21.25 9.49997 20.91 9.83997 20.5 9.83997Z"
                fill="#292D32"
            />
            <path
                d="M18 23.75C16.83 23.75 15.72 23.33 14.87 22.56C14.51 22.26 14.19 21.88 13.93 21.44C13.49 20.72 13.25 19.87 13.25 19C13.25 16.38 15.38 14.25 18 14.25C19.36 14.25 20.66 14.84 21.56 15.86C22.33 16.74 22.75 17.85 22.75 19C22.75 19.87 22.51 20.72 22.06 21.45C21.22 22.87 19.66 23.75 18 23.75ZM18 15.75C16.21 15.75 14.75 17.21 14.75 19C14.75 19.59 14.91 20.17 15.22 20.67C15.39 20.97 15.61 21.22 15.85 21.43C16.45 21.97 17.2 22.25 18 22.25C19.15 22.25 20.19 21.66 20.78 20.68C21.09 20.17 21.25 19.6 21.25 19C21.25 18.22 20.96 17.46 20.44 16.85C19.82 16.15 18.93 15.75 18 15.75Z"
                fill="#292D32"
            />
            <path
                d="M18.7345 20.4628C18.6067 20.6033 18.427 20.6967 18.2151 20.7067L16.8166 20.7728C16.4069 20.7921 16.0519 20.4691 16.0326 20.0594C16.0132 19.6498 16.3362 19.2947 16.7459 19.2754L17.424 19.2434L17.406 17.0639C17.4008 16.6536 17.7386 16.312 18.1489 16.3068C18.5593 16.3016 18.9008 16.6394 18.9061 17.0497L18.9298 19.951C18.925 20.1494 18.8556 20.3296 18.7345 20.4628Z"
                fill="#292D32"
            />
            <path
                d="M15.37 22.75H8C4.35 22.75 2.25 20.65 2.25 17V8.5C2.25 4.85 4.35 2.75 8 2.75H16C19.65 2.75 21.75 4.85 21.75 8.5V16.36C21.75 16.67 21.56 16.95 21.26 17.06C20.97 17.17 20.64 17.09 20.43 16.85C19.81 16.15 18.92 15.75 17.99 15.75C16.2 15.75 14.74 17.21 14.74 19C14.74 19.59 14.9 20.17 15.21 20.67C15.38 20.97 15.6 21.22 15.84 21.43C16.08 21.63 16.17 21.96 16.06 22.26C15.97 22.55 15.69 22.75 15.37 22.75ZM8 4.25C5.14 4.25 3.75 5.64 3.75 8.5V17C3.75 19.86 5.14 21.25 8 21.25H13.82C13.45 20.57 13.25 19.8 13.25 19C13.25 16.38 15.38 14.25 18 14.25C18.79 14.25 19.57 14.45 20.25 14.82V8.5C20.25 5.64 18.86 4.25 16 4.25H8Z"
                fill="#292D32"
            />
        </svg>
    );
};

export default RegisterWorkingCalendar;
