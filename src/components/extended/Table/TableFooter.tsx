import React, { ReactNode } from 'react';

// material-ui
import { Stack, useTheme, Box, IconButton, Typography } from '@mui/material';
import { ChevronLeftOutlined as PrevIcon, ChevronRightOutlined as NextIcon } from '@mui/icons-material';

interface ITableFooterProps {
    download?: ReactNode;
    pagination?: {
        total: number;
        page: number;
        size: number;
    };
    onPageChange?: (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => void;
    onRowsPerPageChange?: (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const TableFooter = (props: ITableFooterProps) => {
    const { download, pagination, onPageChange, onRowsPerPageChange } = props;
    const theme = useTheme();

    return (
        <Stack
            direction="row"
            alignItems="center"
            justifyContent={!download ? 'center' : download && pagination ? 'space-between' : 'end'}
            sx={{ marginTop: !pagination ? '20px' : '', py: 2 }}
        >
            {pagination && onPageChange && onRowsPerPageChange && (
                <Box display="flex" alignItems="center" justifyContent="center" gap={3} sx={{ py: 1 }}>
                    {/* Previous Button */}
                    <IconButton
                        onClick={(event) => onPageChange(event, pagination.page - 1)}
                        disabled={pagination.page <= 0}
                        sx={{
                            width: 40,
                            height: 40,
                            border: '2px solid',
                            borderColor: pagination.page <= 0 ? theme.palette.grey[300] : theme.palette.primary.main,
                            color: pagination.page <= 0 ? theme.palette.grey[400] : theme.palette.primary.main,
                            backgroundColor: 'transparent',
                            '&:hover': {
                                backgroundColor: pagination.page <= 0 ? 'transparent' : theme.palette.primary.main,
                                color: pagination.page <= 0 ? theme.palette.grey[400] : 'white',
                                borderColor: pagination.page <= 0 ? theme.palette.grey[300] : theme.palette.primary.main
                            },
                            '&.Mui-disabled': {
                                backgroundColor: 'transparent',
                                borderColor: theme.palette.grey[300],
                                color: theme.palette.grey[400]
                            },
                            transition: 'all 0.2s ease'
                        }}
                    >
                        <PrevIcon fontSize="small" />
                    </IconButton>

                    {/* Page Info */}
                    <Typography
                        variant="body1"
                        sx={{
                            fontSize: '0.95rem',
                            fontWeight: 500,
                            color: theme.palette.text.primary,
                            minWidth: '100px',
                            textAlign: 'center'
                        }}
                    >
                        <Box component="span" sx={{ color: theme.palette.primary.main, fontWeight: 600 }}>
                            {pagination.page + 1}
                        </Box>
                        <Box component="span" sx={{ color: theme.palette.text.secondary, mx: 0.5 }}>
                            /
                        </Box>
                        <Box component="span" sx={{ color: theme.palette.text.secondary }}>
                            {Math.ceil(pagination.total / pagination.size)} trang
                        </Box>
                    </Typography>

                    {/* Next Button */}
                    <IconButton
                        onClick={(event) => onPageChange(event, pagination.page + 1)}
                        disabled={pagination.page >= Math.ceil(pagination.total / pagination.size) - 1}
                        sx={{
                            width: 40,
                            height: 40,
                            border: '2px solid',
                            borderColor:
                                pagination.page >= Math.ceil(pagination.total / pagination.size) - 1
                                    ? theme.palette.grey[300]
                                    : theme.palette.primary.main,
                            color:
                                pagination.page >= Math.ceil(pagination.total / pagination.size) - 1
                                    ? theme.palette.grey[400]
                                    : theme.palette.primary.main,
                            backgroundColor: 'transparent',
                            '&:hover': {
                                backgroundColor:
                                    pagination.page >= Math.ceil(pagination.total / pagination.size) - 1
                                        ? 'transparent'
                                        : theme.palette.primary.main,
                                color:
                                    pagination.page >= Math.ceil(pagination.total / pagination.size) - 1
                                        ? theme.palette.grey[400]
                                        : 'white',
                                borderColor:
                                    pagination.page >= Math.ceil(pagination.total / pagination.size) - 1
                                        ? theme.palette.grey[300]
                                        : theme.palette.primary.main
                            },
                            '&.Mui-disabled': {
                                backgroundColor: 'transparent',
                                borderColor: theme.palette.grey[300],
                                color: theme.palette.grey[400]
                            },
                            transition: 'all 0.2s ease'
                        }}
                    >
                        <NextIcon fontSize="small" />
                    </IconButton>
                </Box>
            )}
            {download}
        </Stack>
    );
};

export default TableFooter;
