import { TableBody, TableCell, TableRow } from '@mui/material';
import { FormattedMessage } from 'react-intl';

interface ITableEmptyProps {
    bordered?: boolean;
    height?: string;
}

const TableEmpty = (props: ITableEmptyProps) => {
    const { height, bordered = true } = props;

    return (
        <TableBody>
            <TableRow>
                <TableCell align="center" colSpan={100} sx={{ height, borderBottom: bordered ? undefined : 'none' }}>
                    <span>
                        <FormattedMessage id="no-data" />
                    </span>
                </TableCell>
            </TableRow>
        </TableBody>
    );
};

TableEmpty.defaultProps = {
    height: '400px'
};
export default TableEmpty;
