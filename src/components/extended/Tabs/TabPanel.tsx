import DeniedPermissionScreen from 'components/DeniedPermissionScreen';
import { ReactNode } from 'react';

interface ITabPnanelProps {
    index: number;
    value: number;
    children?: ReactNode;
}

const TabPanel = (props: ITabPnanelProps) => {
    const { index, value, children, ...other } = props;

    return value === index ? (
        <DeniedPermissionScreen>
            <div role="tabpanel" id={`full-width-tabpanel-${index}`} aria-labelledby={`full-width-tab-${index}`} {...other}>
                {children}
            </div>
        </DeniedPermissionScreen>
    ) : null;
};

export default TabPanel;
