{
    "compilerOptions": {
        "target": "ES2020",
        "useDefineForClassFields": true,
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "module": "ESNext",
        "skipLibCheck": true,

        /* Bundler mode */
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "noEmit": true,
        "jsx": "react-jsx",

        /* Linting */
        "strict": true,
        "noUnusedLocals": false,
        "noUnusedParameters": false,
        "noFallthroughCasesInSwitch": true,
        "noImplicitAny": true,
        "noImplicitThis": true,
        "strictNullChecks": true,

        /* Path mapping */
        "baseUrl": ".",
        "paths": {
            "@/*": ["src/*"],
            "components/*": ["src/components/*"],
            "pages/*": ["src/pages/*"],
            "utils/*": ["src/utils/*"],
            "services/*": ["src/services/*"],
            "hooks/*": ["src/hooks/*"],
            "constants/*": ["src/constants/*"],
            "types/*": ["src/types/*"],
            "types": ["src/types/index.ts"],
            "store/*": ["src/store/*"],
            "assets/*": ["src/assets/*"],
            "contexts/*": ["src/contexts/*"],
            "layout/*": ["src/layout/*"],
            "themes/*": ["src/themes/*"],
            "themes": ["src/themes/index.tsx"],
            "containers/*": ["src/containers/*"],
            "menu-items/*": ["src/menu-items/*"],
            "menu-items": ["src/menu-items/index.tsx"],
            "app/*": ["src/app/*"],
            "app/hooks": ["src/app/hooks.ts"],
            "app/store": ["src/app/store.ts"],
            "index": ["src/index.tsx"],
            "App": ["src/App.tsx"],
            "Routes": ["src/Routes.tsx"]
        },

        /* Additional */
        "allowJs": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "forceConsistentCasingInFileNames": true,
        "types": ["vite/client"]
    },
    "include": ["src"],
    "exclude": ["node_modules", "dist"]
}
