{"plugins": ["prettier", "@typescript-eslint"], "extends": ["airbnb-typescript", "react-app", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "**/tsconfig.json"}, "settings": {"import/resolver": {"node": {"moduleDirectory": ["node_modules", "src/"]}, "typescript": {"alwaysTryTypes": true}}}, "rules": {"jsx-a11y/no-autofocus": [2, {"ignoreNonDOM": true}], "react/jsx-filename-extension": 0, "no-param-reassign": 0, "react/prop-types": 0, "react/require-default-props": 0, "react/no-array-index-key": 0, "react/react-in-jsx-scope": 0, "react/jsx-props-no-spreading": 0, "import/order": 0, "no-console": 0, "no-shadow": "off", "@typescript-eslint/no-shadow": 0, "@typescript-eslint/naming-convention": 0, "import/no-cycle": 0, "prefer-destructuring": 0, "import/no-unresolved": [2, {"caseSensitive": false}], "@typescript-eslint/no-unused-vars": [1, {"vars": "all", "args": "none"}], "prettier/prettier": [2, {"bracketSpacing": true, "printWidth": 140, "singleQuote": true, "trailingComma": "none", "tabWidth": 4, "useTabs": false, "endOfLine": "auto"}]}}